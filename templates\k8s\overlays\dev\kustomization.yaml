apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: {{PROJECT_ID}}-dev
  annotations:
    config.kubernetes.io/local-config: "true"

namespace: {{NAMESPACE}}

resources:
  - ../../base
  - hpa.yaml
  - pdb.yaml

patchesStrategicMerge:
  - deployment-patch.yaml

commonLabels:
  environment: dev
  deployment-strategy: rolling-fast

commonAnnotations:
  deployment.kubernetes.io/environment: "dev"
  deployment.kubernetes.io/strategy: "rolling-fast"
  deployment.kubernetes.io/cluster: "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"

# Development Environment Configuration
replicas:
  - name: {{PROJECT_ID}}
    count: 2  # Minimum for rolling updates

# Development-specific image configuration
images:
  - name: app-image
    newName: {{CONTAINER_IMAGE_NAME}}
    newTag: {{CONTAINER_IMAGE_TAG}}

# Development-specific config overrides
configMapGenerator:
  - name: {{PROJECT_ID}}-config-dev
    literals:
      - ENVIRONMENT=dev
      - DEBUG=true
      - LOG_LEVEL=debug
      - VERBOSE_LOGGING=true
      - HOT_RELOAD=true
      - FEATURE_REGISTRATION=true
      - FEATURE_DEBUG_MODE=true
      - FEATURE_ANALYTICS=false
      - CACHE_TTL=300
      - CACHE_MAX_SIZE=100
      - RATE_LIMIT_MAX=1000
      - HELMET_ENABLED=false
      - RATE_LIMIT_ENABLED=false
      - TRACING_ENABLED=false
      - PERFORMANCE_MONITORING=false
      - ERROR_REPORTING=false

# Development-specific secret overrides
secretGenerator:
  - name: {{PROJECT_ID}}-secrets-dev
    literals:
      - DEBUG_TOKEN=ZGV2LWRlYnVnLXRva2Vu  # dev-debug-token
