{{#if ENABLE_DATABASE}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{PROJECT_ID}}-postgres
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}-postgres
    component: database
    environment: {{ENVIRONMENT}}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{PROJECT_ID}}-postgres
  template:
    metadata:
      labels:
        app: {{PROJECT_ID}}-postgres
        component: database
        environment: {{ENVIRONMENT}}
    spec:
      containers:
      - name: postgres
        image: postgres:13
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
        - name: POSTGRES_DB
          value: {{DB_NAME}}
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - {{DB_USER}}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - {{DB_USER}}
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
{{/if}}
