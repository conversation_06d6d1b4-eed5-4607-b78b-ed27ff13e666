apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{PROJECT_ID}}-hpa
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: autoscaler
    environment: production
    app-type: {{APP_TYPE}}
  annotations:
    autoscaling.kubernetes.io/environment: "production"
    autoscaling.kubernetes.io/strategy: "conservative"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{PROJECT_ID}}
  minReplicas: 3
  maxReplicas: 10  # Higher scaling for production load
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60  # Scale up at 60% CPU for production safety
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70  # Scale up at 70% memory for production safety
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 300  # Conservative scale up for production
      policies:
      - type: Percent
        value: 25   # Increase by 25% in production
        periodSeconds: 300
      - type: Pods
        value: 1    # Add max 1 pod at once for safety
        periodSeconds: 300
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 900  # 15 minutes before scaling down
      policies:
      - type: Percent
        value: 10   # Scale down by 10% in production
        periodSeconds: 300
      - type: Pods
        value: 1    # Remove max 1 pod at once
        periodSeconds: 300
      selectPolicy: Min
