# Health Check Improvements for GitOps Templates

## Overview

This document outlines the health check improvements implemented in the GitOps templates to prevent degraded ArgoCD application status and ensure reliable deployments.

## Problem Statement

Previously, Spring Boot backend applications were experiencing:
- **HTTP 500 errors** from `/actuator/health` endpoints
- **Failed readiness and liveness probes** causing pod restart loops
- **Degraded ArgoCD application status** due to unhealthy pods
- **Actuator configuration issues** causing health endpoint failures

## Solution Implemented

### 1. TCP-Based Health Checks for Backend Applications

**Before (HTTP-based):**
```yaml
livenessProbe:
  httpGet:
    path: /actuator/health
    port: 8080
  initialDelaySeconds: 90
```

**After (TCP-based):**
```yaml
livenessProbe:
  tcpSocket:
    port: 8080
  initialDelaySeconds: 90
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 3
readinessProbe:
  tcpSocket:
    port: 8080
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3
```

> **Note**: `startupProbe` was removed for compatibility with older Kubernetes versions. The longer `initialDelaySeconds` on liveness probe provides adequate startup time.

### 2. Enhanced Spring Boot Configuration

Added resilient actuator configurations in ConfigMap templates:

```yaml
# Health Check Configuration - Make health endpoint more resilient
MANAGEMENT_ENDPOINT_HEALTH_STATUS_HTTP_MAPPING_DOWN: "503"
MANAGEMENT_ENDPOINT_HEALTH_STATUS_HTTP_MAPPING_OUT_OF_SERVICE: "503"
MANAGEMENT_ENDPOINT_HEALTH_STATUS_HTTP_MAPPING_UNKNOWN: "200"
# Disable problematic health indicators that might cause 500 errors
MANAGEMENT_HEALTH_MAIL_ENABLED: "false"
MANAGEMENT_HEALTH_LDAP_ENABLED: "false"
# Make health checks less strict for development
MANAGEMENT_HEALTH_DEFAULTS_ENABLED: "false"  # Only in dev environment
```

### 3. Application Type Specific Health Checks

| Application Type | Health Check Type | Rationale |
|------------------|-------------------|-----------|
| `react-frontend` | HTTP | Simple static serving, reliable HTTP responses |
| `springboot-backend` | TCP | Avoids actuator configuration issues |
| `node-backend` | TCP | More reliable than HTTP health endpoints |
| `python-backend` | TCP | Consistent with backend approach |
| Other backends | TCP | Default safe approach |

## Benefits

### 1. Reliability
- **TCP probes** are more reliable than HTTP probes for backend services
- **No dependency** on application-level health endpoint configuration
- **Faster detection** of actual service availability

### 2. Startup Time Management
- **Startup probes** allow adequate time for Spring Boot applications to initialize
- **Graduated timeouts** from startup → readiness → liveness
- **Prevents premature restarts** during application startup

### 3. ArgoCD Health Status
- **Consistent healthy status** in ArgoCD UI
- **No more degraded applications** due to health check failures
- **Reliable deployment monitoring**

## Implementation Details

### Template Files Updated

1. **`templates/k8s/deployment.yaml`**
   - Added startup probes for backend applications
   - Switched to TCP-based health checks for backends
   - Maintained HTTP checks for frontend applications

2. **`templates/k8s/configmap.yaml`**
   - Added Spring Boot actuator resilience configurations
   - Environment-specific health check settings
   - Disabled problematic health indicators

3. **Scripts and Documentation**
   - Updated generation scripts with new defaults
   - Enhanced GitHub issue templates
   - Updated environment configuration scripts

### Configuration Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `MANAGEMENT_HEALTH_MAIL_ENABLED` | `false` | Disable mail health indicator |
| `MANAGEMENT_HEALTH_LDAP_ENABLED` | `false` | Disable LDAP health indicator |
| `MANAGEMENT_HEALTH_DEFAULTS_ENABLED` | `false` (dev only) | Disable strict health checks in dev |

## Migration Guide

### For Existing Applications

1. **Update deployment.yaml** to use TCP probes:
   ```yaml
   livenessProbe:
     tcpSocket:
       port: 8080
   ```

2. **Add startup probe** for better startup handling:
   ```yaml
   startupProbe:
     tcpSocket:
       port: 8080
     failureThreshold: 15
   ```

3. **Update ConfigMap** with resilience settings (see template)

### For New Applications

New applications generated from templates will automatically include:
- ✅ TCP-based health checks for backends
- ✅ Startup probes with appropriate timeouts
- ✅ Resilient actuator configurations
- ✅ Environment-specific settings

## Monitoring and Troubleshooting

### Health Check Status
```bash
# Check pod readiness
kubectl get pods -n <namespace>

# Check probe configuration
kubectl describe pod <pod-name> -n <namespace>

# Check events for health check failures
kubectl get events -n <namespace> --sort-by='.lastTimestamp'
```

### ArgoCD Application Health
- Applications should show **Healthy** status consistently
- No more **Degraded** status due to health check failures
- Faster sync and deployment cycles

## Future Considerations

### Optional HTTP Health Checks
For applications that need detailed health monitoring, you can optionally switch back to HTTP probes after ensuring proper actuator configuration:

```yaml
# Only use if actuator is properly configured
livenessProbe:
  httpGet:
    path: /actuator/health/liveness
    port: 8080
```

### Custom Health Indicators
Applications can implement custom health indicators while maintaining TCP probe reliability:

```java
@Component
public class CustomHealthIndicator implements HealthIndicator {
    @Override
    public Health health() {
        // Custom health logic
        return Health.up().build();
    }
}
```

## Conclusion

These health check improvements ensure:
- **Reliable ArgoCD deployments** without degraded status
- **Faster application startup** with appropriate timeouts
- **Consistent health monitoring** across all application types
- **Future-proof templates** that prevent common health check issues

All new applications generated from the updated templates will automatically benefit from these improvements.
