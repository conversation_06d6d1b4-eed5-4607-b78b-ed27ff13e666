apiVersion: v1
kind: LimitRange
metadata:
  name: {{PROJECT_ID}}-limits
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: limit-range
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
  annotations:
    resource.kubernetes.io/environment: "{{ENVIRONMENT}}"
    resource.kubernetes.io/managed-by: "gitops-argocd"
spec:
  limits:
  # Pod-level limits
  - type: Pod
    {{#eq ENVIRONMENT 'dev'}}
    # Development Environment - Flexible limits
    max:
      cpu: "1000m"
      memory: "1Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
    {{/eq}}
    
    {{#eq ENVIRONMENT 'staging'}}
    # Staging Environment - Balanced limits
    max:
      cpu: "1500m"
      memory: "2Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
    {{/eq}}
    
    {{#eq ENVIRONMENT 'production'}}
    # Production Environment - Conservative limits for stability
    max:
      cpu: "1000m"
      memory: "2Gi"
    min:
      cpu: "200m"
      memory: "256Mi"
    {{/eq}}
  
  # Container-level limits
  - type: Container
    {{#eq ENVIRONMENT 'dev'}}
    # Development Environment - Flexible container limits
    default:
      cpu: "200m"
      memory: "256Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    max:
      cpu: "500m"
      memory: "512Mi"
    min:
      cpu: "50m"
      memory: "64Mi"
    {{/eq}}
    
    {{#eq ENVIRONMENT 'staging'}}
    # Staging Environment - Balanced container limits
    default:
      cpu: "400m"
      memory: "512Mi"
    defaultRequest:
      cpu: "200m"
      memory: "256Mi"
    max:
      cpu: "800m"
      memory: "1Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
    {{/eq}}
    
    {{#eq ENVIRONMENT 'production'}}
    # Production Environment - Conservative container limits
    default:
      cpu: "500m"
      memory: "1Gi"
    defaultRequest:
      cpu: "300m"
      memory: "512Mi"
    max:
      cpu: "1000m"
      memory: "2Gi"
    min:
      cpu: "200m"
      memory: "256Mi"
    {{/eq}}
  
  # PersistentVolumeClaim limits
  - type: PersistentVolumeClaim
    {{#eq ENVIRONMENT 'dev'}}
    max:
      storage: "10Gi"
    min:
      storage: "1Gi"
    {{/eq}}
    
    {{#eq ENVIRONMENT 'staging'}}
    max:
      storage: "50Gi"
    min:
      storage: "5Gi"
    {{/eq}}
    
    {{#eq ENVIRONMENT 'production'}}
    max:
      storage: "100Gi"
    min:
      storage: "10Gi"
    {{/eq}}
