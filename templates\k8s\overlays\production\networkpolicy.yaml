apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{PROJECT_ID}}-netpol
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: network-policy
    environment: production
    app-type: {{APP_TYPE}}
  annotations:
    network.kubernetes.io/environment: "production"
    network.kubernetes.io/policy-type: "strict"
spec:
  podSelector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from same namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: {{NAMESPACE}}
    ports:
    - protocol: TCP
      port: {{CONTAINER_PORT}}
  # Allow ingress from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: {{CONTAINER_PORT}}
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS outbound (for external APIs)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  {{#if ENABLE_DATABASE}}
  # Allow database connections
  - to:
    - podSelector:
        matchLabels:
          app: {{PROJECT_ID}}-postgres
    ports:
    - protocol: TCP
      port: 5432
  {{/if}}
  # Production: Restrict HTTP outbound to specific services only
  - to:
    - namespaceSelector:
        matchLabels:
          environment: production
    ports:
    - protocol: TCP
      port: 80
