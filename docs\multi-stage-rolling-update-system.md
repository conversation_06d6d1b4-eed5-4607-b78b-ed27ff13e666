# Multi-Stage Rolling Update Deployment System

This document describes the industry-standard multi-stage rolling update deployment system implemented for the GitOps ArgoCD automation platform.

## 🏗️ Architecture Overview

The system implements a comprehensive Kustomize-based deployment architecture with environment-specific rolling update strategies optimized for multi-cluster deployments.

### Cluster Architecture

- **Management Cluster**: `158b6a47-3e7e-4dca-af0f-05a6e07115af` (ArgoCD control plane)
- **Dev/Staging Cluster**: `6be4e15d-52f9-431d-84ec-ec8cad0dff2d` (4 vCPU, 8GB RAM)
- **Production Cluster**: `e9d23ae8-213c-4746-b379-330f85c0a0cf` (4 vCPU, 8GB RAM)

### Directory Structure

```
templates/
├── k8s/
│   ├── base/                          # Base Kustomize resources
│   │   ├── kustomization.yaml         # Base configuration
│   │   ├── deployment-rolling.yaml    # Rolling update deployment
│   │   ├── service.yaml               # Service definition
│   │   ├── configmap.yaml             # Configuration management
│   │   ├── secret.yaml                # Secret management
│   │   └── resourcequota.yaml         # Resource quotas and limits
│   └── overlays/                      # Environment-specific overlays
│       ├── dev/                       # Development environment
│       │   ├── kustomization.yaml     # Dev configuration
│       │   ├── deployment-patch.yaml  # Dev-specific patches
│       │   ├── hpa.yaml               # Horizontal Pod Autoscaler
│       │   └── pdb.yaml               # Pod Disruption Budget
│       ├── staging/                   # Staging environment
│       │   ├── kustomization.yaml     # Staging configuration
│       │   ├── deployment-patch.yaml  # Staging-specific patches
│       │   ├── hpa.yaml               # Horizontal Pod Autoscaler
│       │   ├── pdb.yaml               # Pod Disruption Budget
│       │   └── networkpolicy.yaml     # Network security policies
│       └── production/                # Production environment
│           ├── kustomization.yaml     # Production configuration
│           ├── deployment-patch.yaml  # Production-specific patches
│           ├── hpa.yaml               # Horizontal Pod Autoscaler
│           ├── pdb.yaml               # Pod Disruption Budget
│           ├── networkpolicy.yaml     # Network security policies
│           └── podsecuritypolicy.yaml # Pod security policies
└── argocd/
    ├── application.yaml               # ArgoCD application (updated for overlays)
    └── project.yaml                   # ArgoCD project
```

## 🚀 Rolling Update Strategies

### Development Environment
- **Strategy**: Rolling Fast
- **Configuration**: 
  - `maxUnavailable: 50%`
  - `maxSurge: 50%`
  - `progressDeadlineSeconds: 120` (2 minutes)
- **Purpose**: Fast iteration and testing
- **Health Checks**: Basic (5s initial, 3s period)
- **Replicas**: 2 minimum
- **Resources**: CPU: 100m-500m, Memory: 128Mi-512Mi
- **Target Cluster**: `6be4e15d-52f9-431d-84ec-ec8cad0dff2d`

### Staging Environment
- **Strategy**: Rolling Controlled
- **Configuration**:
  - `maxUnavailable: 25%`
  - `maxSurge: 25%`
  - `progressDeadlineSeconds: 300` (5 minutes)
- **Purpose**: Pre-production validation with controlled rollouts
- **Health Checks**: Enhanced with startup probes (10s initial, 5s period)
- **Replicas**: 3 minimum
- **Resources**: CPU: 200m-800m, Memory: 256Mi-1Gi
- **Target Cluster**: `6be4e15d-52f9-431d-84ec-ec8cad0dff2d`

### Production Environment
- **Strategy**: Rolling Safe (Zero Downtime)
- **Configuration**:
  - `maxUnavailable: 0%` (Zero downtime)
  - `maxSurge: 33%`
  - `progressDeadlineSeconds: 600` (10 minutes)
- **Purpose**: Safe production rollouts with maximum availability
- **Health Checks**: Comprehensive with extended startup times (30s initial, 10s period)
- **Replicas**: 3 minimum (optimized for 4 vCPU/8GB capacity)
- **Resources**: CPU: 300m-1000m, Memory: 512Mi-2Gi
- **Target Cluster**: `e9d23ae8-213c-4746-b379-330f85c0a0cf`

## 🔒 Security Implementation

### Pod Security Standards
- **Development**: Permissive (for debugging)
- **Staging**: Restricted profile with security contexts
- **Production**: Maximum security with:
  - `runAsNonRoot: true`
  - `readOnlyRootFilesystem: true`
  - `allowPrivilegeEscalation: false`
  - Capability dropping (`drop: ALL`)
  - Seccomp profiles

### Network Policies
- **Staging**: Controlled access with ingress/egress rules
- **Production**: Strict security with minimal required access
- **Development**: No network restrictions (for flexibility)

### RBAC and Service Accounts
- Environment-specific service accounts
- Minimal required permissions
- Role-based access control

## 📊 Resource Management

### Resource Quotas (Optimized for 4 vCPU/8GB Clusters)

#### Development
- CPU Requests: 1000m total, Limits: 2000m
- Memory Requests: 1Gi total, Limits: 2Gi
- Pods: 10 maximum

#### Staging  
- CPU Requests: 1500m total, Limits: 3000m
- Memory Requests: 2Gi total, Limits: 4Gi
- Pods: 15 maximum

#### Production
- CPU Requests: 2000m total, Limits: 3000m (75% cluster capacity)
- Memory Requests: 4Gi total, Limits: 6Gi (75% cluster capacity)
- Pods: 20 maximum

### Horizontal Pod Autoscaling
- **Development**: 2-4 replicas, CPU target 70%
- **Staging**: 2-3 replicas, CPU target 60%
- **Production**: 2-3 replicas, CPU target 50% (conservative)

### Pod Disruption Budgets
- **Development**: `minAvailable: 1`
- **Staging**: `minAvailable: 2`
- **Production**: `minAvailable: 2` (zero downtime support)

## 🔄 Deployment Process

### 1. GitHub Issue Creation
Users create deployment requests using the enhanced issue template with:
- Deployment strategy selection
- Health check strategy options
- Resource profile selection
- Environment-specific configurations

### 2. Manifest Generation
The system generates:
- Base Kustomize resources
- Environment-specific overlays
- ArgoCD applications pointing to overlay paths

### 3. ArgoCD Deployment
- Applications deployed to correct target clusters
- Environment-specific sync policies
- Automated rollback capabilities

### 4. Health Monitoring
- Comprehensive health checks per environment
- Startup, liveness, and readiness probes
- Resource utilization monitoring

## 🛠️ Usage Examples

### Deploy to Development
```bash
# Via GitHub Issue (Recommended)
# Create issue with "Development" environment selected

# Via Script (Advanced)
./scripts/generate-manifests-cicd.ps1 \
  -Environment "dev" \
  -ProjectId "my-app" \
  -AppName "My Application" \
  -DockerImage "myregistry/my-app" \
  -DockerTag "latest"
```

### Deploy to Production
```bash
# Production deployment with zero-downtime strategy
./scripts/generate-manifests-cicd.ps1 \
  -Environment "production" \
  -ProjectId "my-app" \
  -AppName "My Application" \
  -DockerImage "myregistry/my-app" \
  -DockerTag "v1.0.0"
```

## 📈 Success Criteria

### Development
- Deployment time: <2 minutes
- Basic validation and automated recovery
- Efficient resource usage (~50% CPU, ~25% memory)

### Staging
- Deployment time: <5 minutes
- Comprehensive testing and semi-automated oversight
- Production-like resource allocation (~60% CPU, ~40% memory)

### Production
- Deployment time: <10 minutes
- Zero-downtime updates with maximum safety
- Optimal resource utilization (~75% CPU, ~75% memory)
- 25% resource buffer for system overhead

## 🔧 Configuration Options

### Environment Variables
All environments support comprehensive configuration through:
- ConfigMaps with environment-specific values
- Secrets with proper base64 encoding
- Dynamic database host configuration
- Feature flags per environment

### Health Check Strategies
- **Fast**: Development (5s initial, 3s period)
- **Balanced**: Staging (10s initial, 5s period)  
- **Comprehensive**: Production (30s initial, 10s period)

### Resource Profiles
- **Minimal**: Dev (100m CPU, 128Mi RAM)
- **Balanced**: Staging (200m CPU, 256Mi RAM)
- **Optimized**: Production (300m CPU, 512Mi RAM)

## 🚨 Troubleshooting

### Common Issues
1. **Deployment Timeout**: Check resource limits and health check timeouts
2. **Pod Startup Failures**: Verify startup probe configuration
3. **Resource Constraints**: Review resource quotas and cluster capacity
4. **Network Issues**: Check network policies and ingress configuration

### Rollback Procedures
- ArgoCD automatic rollback on deployment failure
- Manual rollback via ArgoCD UI or CLI
- Environment-specific rollback policies

## 📚 Related Documentation
- [Cross-Cluster Setup Guide](cross-cluster-setup.md)
- [Application Types Guide](application-types-guide.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)
- [Security Best Practices](security-best-practices.md)
