apiVersion: v1
kind: LimitRange
metadata:
  name: ai-react-frontend-limits
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: limit-range
    environment: dev
    app-type: react-frontend
  annotations:
    resource.kubernetes.io/environment: "dev"
    resource.kubernetes.io/managed-by: "gitops-argocd"
spec:
  limits:
  # Pod-level limits
  - type: Pod
    # Development Environment - Flexible limits
    max:
      cpu: "1000m"
      memory: "1Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
  # Container-level limits
  - type: Container
    # Development Environment - Flexible container limits
    default:
      cpu: "200m"
      memory: "256Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    max:
      cpu: "500m"
      memory: "512Mi"
    min:
      cpu: "50m"
      memory: "64Mi"
  # PersistentVolumeClaim limits
  - type: PersistentVolumeClaim
    max:
      storage: "10Gi"
    min:
      storage: "1Gi"
