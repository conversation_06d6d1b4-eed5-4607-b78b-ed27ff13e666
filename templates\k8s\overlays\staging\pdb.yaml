apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{PROJECT_ID}}-pdb
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: disruption-budget
    environment: staging
    app-type: {{APP_TYPE}}
  annotations:
    policy.kubernetes.io/environment: "staging"
    policy.kubernetes.io/strategy: "balanced"
spec:
  # Staging - Balanced approach for validation
  minAvailable: 50%  # Keep at least 50% of pods running
  selector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
      environment: staging
