# Troubleshooting Degraded Health Status for ai-react-frontend

## Overview

This guide helps diagnose and fix degraded health status in ArgoCD for the ai-react-frontend application.

## Common Causes and Solutions

### 1. Image Pull Issues ⚠️ **MOST COMMON**

**Symptoms:**
- Pods stuck in `ImagePullBackOff` or `ErrImagePull`
- ArgoCD shows degraded health
- No running pods

**Diagnosis:**
```bash
kubectl get pods -n ai-react-frontend-dev
kubectl describe pod <pod-name> -n ai-react-frontend-dev
```

**Solution:**
- ✅ **FIXED**: Updated image tag from `arc-runners` to `latest`
- Verify the image exists: `docker pull saipriya104/ai-react-frontend:latest`

### 2. Health Check Failures

**Symptoms:**
- Pods running but not ready
- Readiness/Liveness probe failures
- Frequent pod restarts

**Diagnosis:**
```bash
kubectl describe pod <pod-name> -n ai-react-frontend-dev
kubectl logs <pod-name> -n ai-react-frontend-dev
```

**Solution:**
- ✅ **FIXED**: Added startup probe with 100-second timeout
- ✅ **FIXED**: Improved health check timing and failure thresholds

### 3. Pod Disruption Budget Conflicts

**Symptoms:**
- Rolling updates fail
- Pods cannot be evicted
- Deployment stuck

**Diagnosis:**
```bash
kubectl get pdb -n ai-react-frontend-dev
kubectl describe pdb ai-react-frontend-pdb -n ai-react-frontend-dev
```

**Solution:**
- ✅ **FIXED**: Changed from `minAvailable: 1` to `maxUnavailable: 1`

### 4. Service Endpoint Issues

**Symptoms:**
- Service has no endpoints
- Network connectivity issues
- Load balancer not working

**Diagnosis:**
```bash
kubectl get endpoints -n ai-react-frontend-dev
kubectl get svc -n ai-react-frontend-dev
```

**Solution:**
- ✅ **FIXED**: Corrected service name reference in ConfigMap

## Quick Diagnostic Commands

### Check Overall Status
```bash
# Check ArgoCD application status
kubectl get application ai-react-frontend -n argocd

# Check all resources in namespace
kubectl get all -n ai-react-frontend-dev

# Check pod details
kubectl describe pods -n ai-react-frontend-dev -l app=ai-react-frontend
```

### Check Health Probes
```bash
# Check probe configuration
kubectl get deployment ai-react-frontend -n ai-react-frontend-dev -o yaml | grep -A 10 -B 5 "probe"

# Check probe failures in events
kubectl get events -n ai-react-frontend-dev --sort-by='.lastTimestamp' | grep -i probe
```

### Check Image Issues
```bash
# Check image pull status
kubectl describe pods -n ai-react-frontend-dev | grep -A 5 -B 5 "image"

# Verify image exists
docker manifest inspect saipriya104/ai-react-frontend:latest
```

## Automated Diagnosis

Use the provided diagnostic script:

```bash
# Run diagnosis
./ai-react-frontend/scripts/diagnose-health.ps1

# Run diagnosis with automatic fixes
./ai-react-frontend/scripts/diagnose-health.ps1 -Fix
```

## Manual Fix Steps

### Step 1: Sync ArgoCD Application
```bash
# Force sync the application
kubectl patch application ai-react-frontend -n argocd --type merge -p '{"operation":{"sync":{"syncStrategy":{"force":true}}}}'

# Or use ArgoCD CLI
argocd app sync ai-react-frontend --force
```

### Step 2: Restart Deployment
```bash
# Restart the deployment
kubectl rollout restart deployment/ai-react-frontend -n ai-react-frontend-dev

# Wait for rollout
kubectl rollout status deployment/ai-react-frontend -n ai-react-frontend-dev
```

### Step 3: Check Pod Logs
```bash
# Check application logs
kubectl logs -f deployment/ai-react-frontend -n ai-react-frontend-dev

# Check init container logs
kubectl logs <pod-name> -c nginx-cache-init -n ai-react-frontend-dev
```

## Verification Steps

After applying fixes, verify the application is healthy:

### 1. Check Pod Status
```bash
kubectl get pods -n ai-react-frontend-dev -l app=ai-react-frontend
```
Expected: All pods should be `Running` and `Ready`

### 2. Check Service Endpoints
```bash
kubectl get endpoints ai-react-frontend -n ai-react-frontend-dev
```
Expected: Should show ready addresses

### 3. Check ArgoCD Status
```bash
kubectl get application ai-react-frontend -n argocd -o jsonpath='{.status.health.status}'
```
Expected: Should return `Healthy`

### 4. Test Application Connectivity
```bash
# Port forward to test locally
kubectl port-forward svc/ai-react-frontend 8080:8080 -n ai-react-frontend-dev

# Test health endpoint
curl http://localhost:8080/
```

## Prevention Measures

### 1. Image Management
- Use specific version tags instead of `latest` in production
- Implement image scanning and validation
- Set up image pull secrets if using private registries

### 2. Health Check Optimization
- Configure appropriate startup probes for slow-starting applications
- Set realistic timeout and failure thresholds
- Monitor health check metrics

### 3. Resource Management
- Set appropriate resource requests and limits
- Monitor resource usage and adjust as needed
- Use HPA for automatic scaling

### 4. Monitoring and Alerting
- Set up monitoring for pod health and application metrics
- Configure alerts for deployment failures
- Implement log aggregation for troubleshooting

## Related Documentation

- [Health Check Improvements](./health-check-improvements.md)
- [ArgoCD Application Configuration](../argocd/application.yaml)
- [Kubernetes Deployment Configuration](../k8s/base/deployment-rolling.yaml)

## Support

If issues persist after following this guide:

1. Check the ArgoCD UI for detailed error messages
2. Review application logs for specific error details
3. Verify cluster resources and capacity
4. Consider reaching out to the platform team for assistance
