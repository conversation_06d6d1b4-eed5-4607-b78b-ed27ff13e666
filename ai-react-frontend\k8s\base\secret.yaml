apiVersion: v1
kind: Secret
metadata:
  name: ai-react-frontend-secrets
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: secrets
    environment: dev
    app-type: react-frontend
  annotations:
    config.kubernetes.io/local-config: "true"
type: Opaque
data:
  # React Frontend - Minimal secrets (typically API keys for build-time)
  # Most secrets are handled by the backend API
  # Add custom secrets here if needed for your React app
  # Build-time API keys (if needed)
  REACT_APP_API_KEY: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgQVBJIGtleQ==  # PLACEHOLDER
  # Analytics keys (if needed)
  REACT_APP_ANALYTICS_KEY: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgYW5hbHl0aWNzIGtleQ==  # PLACEHOLDER
  # Environment-specific secret overrides (simplified to avoid template processing issues)
  # Development environment secrets (for dev environment)
  # DEBUG_TOKEN: ZGV2LWRlYnVnLXRva2Vu  # dev-debug-token
  # Staging environment secrets (for staging environment)
  # STAGING_API_KEY: c3RhZ2luZy1hcGkta2V5  # staging-api-key
  # Production environment secrets (for production environment)
  # PROD_ENCRYPTION_KEY: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgcHJvZHVjdGlvbiBlbmNyeXB0aW9uIGtleQ==  # PLACEHOLDER
