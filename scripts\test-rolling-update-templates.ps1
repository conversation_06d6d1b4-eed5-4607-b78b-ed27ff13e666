#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Test script for the new multi-stage rolling update deployment system
    
.DESCRIPTION
    This script tests the new Kustomize-based rolling update templates across all environments
    to ensure proper generation and compatibility with the existing GitOps automation.
    
.PARAMETER Environment
    Environment to test (dev, staging, production, or all)
    
.PARAMETER ProjectId
    Test project identifier
    
.PARAMETER CleanUp
    Remove test files after validation
    
.EXAMPLE
    ./test-rolling-update-templates.ps1 -Environment "all" -CleanUp
    
.EXAMPLE
    ./test-rolling-update-templates.ps1 -Environment "production" -ProjectId "test-app"
#>

param(
    [Parameter(Mandatory = $false)]
    [ValidateSet("dev", "staging", "production", "all")]
    [string]$Environment = "all",
    
    [Parameter(Mandatory = $false)]
    [string]$ProjectId = "test-rolling-update",
    
    [Parameter(Mandatory = $false)]
    [switch]$CleanUp
)

# Test configuration
$testConfig = @{
    AppName = "Test Rolling Update App"
    ProjectId = $ProjectId
    DockerImage = "nginx"
    DockerTag = "latest"
    AppType = "web-app"
    ContainerPort = 80
    HealthCheckPath = "/"
    EnableDatabase = $false
}

function Write-TestStatus {
    param(
        [string]$Message,
        [string]$Status = "INFO"
    )
    
    $color = switch ($Status) {
        "SUCCESS" { "Green" }
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        default { "White" }
    }
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $color
}

function Test-Environment {
    param(
        [string]$Env
    )
    
    Write-TestStatus "Testing environment: $Env" "INFO"
    
    try {
        # Test manifest generation
        $scriptPath = Join-Path $PSScriptRoot "generate-manifests-cicd.ps1"
        
        $params = @{
            AppName = $testConfig.AppName
            ProjectId = $testConfig.ProjectId
            Environment = $Env
            DockerImage = $testConfig.DockerImage
            DockerTag = $testConfig.DockerTag
            AppType = $testConfig.AppType
            ContainerPort = $testConfig.ContainerPort
            HealthCheckPath = $testConfig.HealthCheckPath
            EnableDatabase = $testConfig.EnableDatabase
            OutputDir = "test-output"
        }
        
        Write-TestStatus "Generating manifests for $Env environment..." "INFO"
        & $scriptPath @params
        
        if ($LASTEXITCODE -eq 0) {
            Write-TestStatus "Manifest generation successful for $Env" "SUCCESS"
            
            # Validate generated files
            $projectDir = Join-Path "test-output" $testConfig.ProjectId
            $k8sBaseDir = Join-Path $projectDir "k8s/base"
            $k8sEnvDir = Join-Path $projectDir "k8s/overlays/$Env"
            $argoCdDir = Join-Path $projectDir "argocd"
            
            # Check base files
            $baseFiles = @(
                "kustomization.yaml",
                "deployment-rolling.yaml",
                "service.yaml",
                "configmap.yaml",
                "secret.yaml",
                "resourcequota.yaml"
            )
            
            foreach ($file in $baseFiles) {
                $filePath = Join-Path $k8sBaseDir $file
                if (Test-Path $filePath) {
                    Write-TestStatus "✓ Base file exists: $file" "SUCCESS"
                } else {
                    Write-TestStatus "✗ Missing base file: $file" "ERROR"
                    return $false
                }
            }
            
            # Check environment-specific files
            $envFiles = @(
                "kustomization.yaml",
                "deployment-patch.yaml",
                "hpa.yaml",
                "pdb.yaml"
            )
            
            if ($Env -eq "staging" -or $Env -eq "production") {
                $envFiles += "networkpolicy.yaml"
            }
            
            if ($Env -eq "production") {
                $envFiles += "podsecuritypolicy.yaml"
            }
            
            foreach ($file in $envFiles) {
                $filePath = Join-Path $k8sEnvDir $file
                if (Test-Path $filePath) {
                    Write-TestStatus "✓ Environment file exists: $file" "SUCCESS"
                } else {
                    Write-TestStatus "✗ Missing environment file: $file" "ERROR"
                    return $false
                }
            }
            
            # Check ArgoCD files
            $argoCdFiles = @("application.yaml", "project.yaml")
            foreach ($file in $argoCdFiles) {
                $filePath = Join-Path $argoCdDir $file
                if (Test-Path $filePath) {
                    Write-TestStatus "✓ ArgoCD file exists: $file" "SUCCESS"
                } else {
                    Write-TestStatus "✗ Missing ArgoCD file: $file" "ERROR"
                    return $false
                }
            }
            
            # Validate YAML syntax
            Write-TestStatus "Validating YAML syntax..." "INFO"
            $yamlFiles = Get-ChildItem -Path $projectDir -Recurse -Filter "*.yaml"
            foreach ($yamlFile in $yamlFiles) {
                try {
                    $content = Get-Content $yamlFile.FullName -Raw
                    # Basic YAML validation (check for common issues)
                    if ($content -match '\{\{[^}]+\}\}') {
                        Write-TestStatus "⚠️  Unprocessed template variables in: $($yamlFile.Name)" "WARNING"
                    } else {
                        Write-TestStatus "✓ YAML syntax valid: $($yamlFile.Name)" "SUCCESS"
                    }
                } catch {
                    Write-TestStatus "✗ YAML syntax error in: $($yamlFile.Name)" "ERROR"
                    return $false
                }
            }
            
            # Validate environment-specific configurations
            Write-TestStatus "Validating environment-specific configurations..." "INFO"
            
            # Check deployment strategy
            $deploymentPatch = Join-Path $k8sEnvDir "deployment-patch.yaml"
            $deploymentContent = Get-Content $deploymentPatch -Raw
            
            switch ($Env) {
                "dev" {
                    if ($deploymentContent -match "maxUnavailable: 50%" -and $deploymentContent -match "maxSurge: 50%") {
                        Write-TestStatus "✓ Dev rolling strategy configured correctly" "SUCCESS"
                    } else {
                        Write-TestStatus "✗ Dev rolling strategy misconfigured" "ERROR"
                        return $false
                    }
                }
                "staging" {
                    if ($deploymentContent -match "maxUnavailable: 25%" -and $deploymentContent -match "maxSurge: 25%") {
                        Write-TestStatus "✓ Staging rolling strategy configured correctly" "SUCCESS"
                    } else {
                        Write-TestStatus "✗ Staging rolling strategy misconfigured" "ERROR"
                        return $false
                    }
                }
                "production" {
                    if ($deploymentContent -match "maxUnavailable: 0%" -and $deploymentContent -match "maxSurge: 33%") {
                        Write-TestStatus "✓ Production rolling strategy configured correctly" "SUCCESS"
                    } else {
                        Write-TestStatus "✗ Production rolling strategy misconfigured" "ERROR"
                        return $false
                    }
                }
            }
            
            # Check cluster targeting in ArgoCD application
            $appContent = Get-Content (Join-Path $argoCdDir "application.yaml") -Raw
            $expectedCluster = switch ($Env) {
                "dev" { "6be4e15d-52f9-431d-84ec-ec8cad0dff2d" }
                "staging" { "6be4e15d-52f9-431d-84ec-ec8cad0dff2d" }
                "production" { "e9d23ae8-213c-4746-b379-330f85c0a0cf" }
            }
            
            if ($appContent -match $expectedCluster) {
                Write-TestStatus "✓ Cluster targeting configured correctly for $Env" "SUCCESS"
            } else {
                Write-TestStatus "✗ Cluster targeting misconfigured for $Env" "ERROR"
                return $false
            }
            
            Write-TestStatus "Environment $Env validation completed successfully" "SUCCESS"
            return $true
            
        } else {
            Write-TestStatus "Manifest generation failed for $Env" "ERROR"
            return $false
        }
        
    } catch {
        Write-TestStatus "Error testing environment $Env`: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main test execution
Write-TestStatus "Starting multi-stage rolling update template tests" "INFO"
Write-TestStatus "Test Project ID: $ProjectId" "INFO"

$environments = if ($Environment -eq "all") { @("dev", "staging", "production") } else { @($Environment) }
$allTestsPassed = $true

foreach ($env in $environments) {
    $testResult = Test-Environment -Env $env
    if (-not $testResult) {
        $allTestsPassed = $false
    }
    Write-TestStatus "----------------------------------------" "INFO"
}

# Clean up test files if requested
if ($CleanUp -and (Test-Path "test-output")) {
    Write-TestStatus "Cleaning up test files..." "INFO"
    Remove-Item -Path "test-output" -Recurse -Force
    Write-TestStatus "Test files cleaned up" "SUCCESS"
}

# Final results
if ($allTestsPassed) {
    Write-TestStatus "🎉 All tests passed! Multi-stage rolling update system is ready." "SUCCESS"
    exit 0
} else {
    Write-TestStatus "❌ Some tests failed. Please review the errors above." "ERROR"
    exit 1
}
