apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
metadata:
  name: ai-react-frontend-base
  annotations:
    config.kubernetes.io/local-config: "true"
resources:
  - deployment-rolling.yaml
  - service.yaml
  - service-headless.yaml
  - configmap.yaml
  - secret.yaml
  - resourcequota.yaml
  - limitrange.yaml
commonLabels:
  app: ai-react-frontend
  app.kubernetes.io/name: ai-react-frontend
  app.kubernetes.io/part-of: ai-react-frontend
  app.kubernetes.io/managed-by: argocd
  environment: dev
  app-type: react-frontend
commonAnnotations:
  app.kubernetes.io/version: "1.0.0"
  deployment.kubernetes.io/revision: "1"
namespace: ai-react-frontend-dev
images:
  - name: app-image
    newName: saipriya104/ai-react-frontend
    newTag: latest
replicas:
  - name: ai-react-frontend
    count: 1
