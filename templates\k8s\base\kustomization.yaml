apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: {{PROJECT_ID}}-base
  annotations:
    config.kubernetes.io/local-config: "true"

resources:
  - deployment-rolling.yaml
  - service.yaml
  {{#eq APP_TYPE 'react-frontend'}}
  - service-headless.yaml
  {{/eq}}
  - configmap.yaml
  - secret.yaml
  - resourcequota.yaml
  - limitrange.yaml
  {{#if ENABLE_DATABASE}}
  - ../../../k8s/postgres-deployment.yaml
  - ../../../k8s/postgres-service.yaml
  - ../../../k8s/postgres-pvc.yaml
  {{/if}}

commonLabels:
  app: {{PROJECT_ID}}
  app.kubernetes.io/name: {{PROJECT_ID}}
  app.kubernetes.io/part-of: {{PROJECT_ID}}
  app.kubernetes.io/managed-by: argocd
  environment: {{ENVIRONMENT}}
  app-type: {{APP_TYPE}}

commonAnnotations:
  app.kubernetes.io/version: "1.0.0"
  deployment.kubernetes.io/revision: "1"

namespace: {{NAMESPACE}}

images:
  - name: app-image
    newName: {{CONTAINER_IMAGE_NAME}}
    newTag: {{CONTAINER_IMAGE_TAG}}

replicas:
  - name: {{PROJECT_ID}}
    count: {{REPLICAS}}
