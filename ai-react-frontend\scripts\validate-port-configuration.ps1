#!/usr/bin/env pwsh
# Validation script for ai-react-frontend port configuration

param(
    [string]$Namespace = "ai-react-frontend-dev",
    [string]$AppName = "ai-react-frontend",
    [int]$ExpectedPort = 80,
    [switch]$Fix,
    [switch]$Verbose
)

Write-Host "🔍 Validating ai-react-frontend port configuration..." -ForegroundColor Cyan
Write-Host "Expected Port: $ExpectedPort" -ForegroundColor Gray
Write-Host ""

$issues = @()
$validationPassed = $true

# Function to check file content for port references
function Test-FilePortConfiguration {
    param(
        [string]$FilePath,
        [string]$Description,
        [string[]]$ExpectedPatterns,
        [string[]]$UnexpectedPatterns
    )
    
    Write-Host "📄 Checking $Description..." -ForegroundColor Yellow
    
    if (-not (Test-Path $FilePath)) {
        Write-Host "  ❌ File not found: $FilePath" -ForegroundColor Red
        $script:issues += "File not found: $FilePath"
        $script:validationPassed = $false
        return
    }
    
    $content = Get-Content $FilePath -Raw
    $fileIssues = @()
    
    # Check for expected patterns
    foreach ($pattern in $ExpectedPatterns) {
        if ($content -notmatch $pattern) {
            $fileIssues += "Missing expected pattern: $pattern"
        } else {
            Write-Host "  ✅ Found expected pattern: $pattern" -ForegroundColor Green
        }
    }
    
    # Check for unexpected patterns (old port references)
    foreach ($pattern in $UnexpectedPatterns) {
        if ($content -match $pattern) {
            $fileIssues += "Found unexpected pattern: $pattern"
        }
    }
    
    if ($fileIssues.Count -eq 0) {
        Write-Host "  ✅ Port configuration is correct" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Issues found:" -ForegroundColor Red
        foreach ($issue in $fileIssues) {
            Write-Host "    - $issue" -ForegroundColor Red
            $script:issues += "$Description - $issue"
        }
        $script:validationPassed = $false
    }
}

# Function to check Kubernetes resources
function Test-KubernetesPortConfiguration {
    Write-Host "🔧 Checking Kubernetes resources..." -ForegroundColor Yellow
    
    try {
        # Check deployment
        $deployment = kubectl get deployment $AppName -n $Namespace -o json 2>$null | ConvertFrom-Json
        if ($deployment) {
            $containerPort = $deployment.spec.template.spec.containers[0].ports[0].containerPort
            if ($containerPort -eq $ExpectedPort) {
                Write-Host "  ✅ Deployment container port: $containerPort" -ForegroundColor Green
            } else {
                Write-Host "  ❌ Deployment container port: $containerPort (expected: $ExpectedPort)" -ForegroundColor Red
                $script:issues += "Deployment container port mismatch: $containerPort vs $ExpectedPort"
                $script:validationPassed = $false
            }
            
            # Check health probe ports
            $livenessPort = $deployment.spec.template.spec.containers[0].livenessProbe.httpGet.port
            $readinessPort = $deployment.spec.template.spec.containers[0].readinessProbe.httpGet.port
            
            if ($livenessPort -eq $ExpectedPort) {
                Write-Host "  ✅ Liveness probe port: $livenessPort" -ForegroundColor Green
            } else {
                Write-Host "  ❌ Liveness probe port: $livenessPort (expected: $ExpectedPort)" -ForegroundColor Red
                $script:issues += "Liveness probe port mismatch: $livenessPort vs $ExpectedPort"
                $script:validationPassed = $false
            }
            
            if ($readinessPort -eq $ExpectedPort) {
                Write-Host "  ✅ Readiness probe port: $readinessPort" -ForegroundColor Green
            } else {
                Write-Host "  ❌ Readiness probe port: $readinessPort (expected: $ExpectedPort)" -ForegroundColor Red
                $script:issues += "Readiness probe port mismatch: $readinessPort vs $ExpectedPort"
                $script:validationPassed = $false
            }
        } else {
            Write-Host "  ⚠️  Deployment not found or not accessible" -ForegroundColor Yellow
        }
        
        # Check service
        $service = kubectl get service $AppName -n $Namespace -o json 2>$null | ConvertFrom-Json
        if ($service) {
            $servicePort = $service.spec.ports[0].port
            $targetPort = $service.spec.ports[0].targetPort
            
            if ($servicePort -eq $ExpectedPort -and $targetPort -eq $ExpectedPort) {
                Write-Host "  ✅ Service ports: $servicePort -> $targetPort" -ForegroundColor Green
            } else {
                Write-Host "  ❌ Service ports: $servicePort -> $targetPort (expected: $ExpectedPort -> $ExpectedPort)" -ForegroundColor Red
                $script:issues += "Service port mismatch: $servicePort -> $targetPort vs $ExpectedPort -> $ExpectedPort"
                $script:validationPassed = $false
            }
        } else {
            Write-Host "  ⚠️  Service not found or not accessible" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "  ❌ Error checking Kubernetes resources: $($_.Exception.Message)" -ForegroundColor Red
        $script:issues += "Kubernetes resource check failed: $($_.Exception.Message)"
        $script:validationPassed = $false
    }
}

# Function to test connectivity
function Test-ApplicationConnectivity {
    Write-Host "🌐 Testing application connectivity..." -ForegroundColor Yellow
    
    try {
        # Check if pods are running
        $pods = kubectl get pods -n $Namespace -l app=$AppName --no-headers 2>$null
        if (-not $pods) {
            Write-Host "  ⚠️  No pods found for testing connectivity" -ForegroundColor Yellow
            return
        }
        
        $runningPods = $pods -split "`n" | Where-Object { $_ -match "Running" -and $_ -match "1/1" }
        if (-not $runningPods) {
            Write-Host "  ⚠️  No running pods available for testing connectivity" -ForegroundColor Yellow
            return
        }
        
        # Test port forward (non-blocking)
        Write-Host "  Testing port connectivity..." -ForegroundColor Gray
        $testPort = 8081  # Use different port to avoid conflicts
        
        # Start port-forward in background
        $portForwardJob = Start-Job -ScriptBlock {
            param($svc, $ns, $localPort, $remotePort)
            kubectl port-forward "svc/$svc" "${localPort}:${remotePort}" -n $ns
        } -ArgumentList $AppName, $Namespace, $testPort, $ExpectedPort
        
        Start-Sleep -Seconds 3
        
        # Test connectivity
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$testPort" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "  ✅ Application is responding on port $ExpectedPort" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️  Application responded with status: $($response.StatusCode)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  ⚠️  Could not connect to application: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        
        # Clean up port-forward
        Stop-Job $portForwardJob -ErrorAction SilentlyContinue
        Remove-Job $portForwardJob -ErrorAction SilentlyContinue
        
    } catch {
        Write-Host "  ❌ Error testing connectivity: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main validation
Write-Host "Starting validation..." -ForegroundColor Gray
Write-Host ""

# Check configuration files
Test-FilePortConfiguration `
    -FilePath "ai-react-frontend/k8s/base/configmap.yaml" `
    -Description "ConfigMap" `
    -ExpectedPatterns @('PORT: "80"') `
    -UnexpectedPatterns @('PORT: "8080"', 'port.*8080')

Test-FilePortConfiguration `
    -FilePath "ai-react-frontend/k8s/base/deployment-rolling.yaml" `
    -Description "Base Deployment" `
    -ExpectedPatterns @('containerPort: 80', 'port: 80', 'value: "80"') `
    -UnexpectedPatterns @('containerPort: 8080', 'port: 8080', 'value: "8080"')

Test-FilePortConfiguration `
    -FilePath "ai-react-frontend/k8s/base/service.yaml" `
    -Description "Service" `
    -ExpectedPatterns @('port: 80', 'targetPort: 80') `
    -UnexpectedPatterns @('port: 8080', 'targetPort: 8080')

Test-FilePortConfiguration `
    -FilePath "ai-react-frontend/k8s/base/service-headless.yaml" `
    -Description "Headless Service" `
    -ExpectedPatterns @('port: 80', 'targetPort: 80') `
    -UnexpectedPatterns @('port: 8080', 'targetPort: 8080')

Test-FilePortConfiguration `
    -FilePath "ai-react-frontend/k8s/overlays/dev/deployment-patch.yaml" `
    -Description "Dev Deployment Patch" `
    -ExpectedPatterns @('port: 80') `
    -UnexpectedPatterns @('port: 8080')

# Check Kubernetes resources if kubectl is available
try {
    kubectl version --client --short | Out-Null
    Test-KubernetesPortConfiguration
} catch {
    Write-Host "⚠️  kubectl not available, skipping Kubernetes resource validation" -ForegroundColor Yellow
}

# Test connectivity if requested
if ($Verbose) {
    Test-ApplicationConnectivity
}

# Summary
Write-Host ""
Write-Host "📊 Validation Summary" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

if ($validationPassed) {
    Write-Host "✅ All port configurations are correct!" -ForegroundColor Green
    Write-Host "   Expected port $ExpectedPort is configured throughout the application." -ForegroundColor Green
} else {
    Write-Host "❌ Port configuration issues found:" -ForegroundColor Red
    foreach ($issue in $issues) {
        Write-Host "   - $issue" -ForegroundColor Red
    }
    
    if ($Fix) {
        Write-Host ""
        Write-Host "🔧 Auto-fix is not implemented. Please manually fix the issues above." -ForegroundColor Yellow
        Write-Host "   Refer to the troubleshooting guide for detailed instructions." -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. If issues found, fix the configuration files manually" -ForegroundColor White
Write-Host "2. Apply changes: kubectl apply -k ai-react-frontend/k8s/overlays/dev" -ForegroundColor White
Write-Host "3. Restart deployment: kubectl rollout restart deployment/$AppName -n $Namespace" -ForegroundColor White
Write-Host "4. Monitor: kubectl get pods -n $Namespace -w" -ForegroundColor White

exit $(if ($validationPassed) { 0 } else { 1 })
