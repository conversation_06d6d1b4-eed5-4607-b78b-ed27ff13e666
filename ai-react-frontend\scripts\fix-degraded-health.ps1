#!/usr/bin/env pwsh
# Quick fix script for ai-react-frontend degraded health issues

param(
    [string]$Namespace = "ai-react-frontend-dev",
    [string]$AppName = "ai-react-frontend",
    [switch]$Force,
    [switch]$WaitForSync
)

Write-Host "🔧 Fixing ai-react-frontend degraded health issues..." -ForegroundColor Cyan

# Function to check if kubectl is available
function Test-KubectlAvailable {
    try {
        kubectl version --client --short | Out-Null
        return $true
    } catch {
        Write-Host "❌ kubectl is not available or not configured" -ForegroundColor Red
        return $false
    }
}

# Function to sync ArgoCD application
function Sync-ArgoCDApplication {
    Write-Host "`n🔄 Syncing ArgoCD application..." -ForegroundColor Yellow
    
    try {
        if ($Force) {
            Write-Host "Force syncing application..." -ForegroundColor Yellow
            kubectl patch application $AppName -n argocd --type merge -p '{"operation":{"sync":{"syncStrategy":{"force":true}}}}'
        } else {
            Write-Host "Regular sync..." -ForegroundColor Yellow
            kubectl patch application $AppName -n argocd --type merge -p '{"operation":{"sync":{}}}'
        }
        
        if ($WaitForSync) {
            Write-Host "Waiting for sync to complete..." -ForegroundColor Yellow
            $timeout = 300 # 5 minutes
            $elapsed = 0
            $interval = 10
            
            do {
                Start-Sleep -Seconds $interval
                $elapsed += $interval
                
                $syncStatus = kubectl get application $AppName -n argocd -o jsonpath='{.status.sync.status}' 2>$null
                $healthStatus = kubectl get application $AppName -n argocd -o jsonpath='{.status.health.status}' 2>$null
                
                Write-Host "Sync Status: $syncStatus, Health Status: $healthStatus" -ForegroundColor Gray
                
                if ($syncStatus -eq "Synced" -and $healthStatus -eq "Healthy") {
                    Write-Host "✅ Application is synced and healthy!" -ForegroundColor Green
                    return $true
                }
                
            } while ($elapsed -lt $timeout)
            
            Write-Host "⚠️  Timeout waiting for sync to complete" -ForegroundColor Yellow
            return $false
        }
        
        return $true
    } catch {
        Write-Host "❌ Failed to sync ArgoCD application: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to restart deployment
function Restart-Deployment {
    Write-Host "`n🔄 Restarting deployment..." -ForegroundColor Yellow
    
    try {
        kubectl rollout restart deployment/$AppName -n $Namespace
        
        Write-Host "Waiting for rollout to complete..." -ForegroundColor Yellow
        kubectl rollout status deployment/$AppName -n $Namespace --timeout=300s
        
        return $true
    } catch {
        Write-Host "❌ Failed to restart deployment: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to check final status
function Check-FinalStatus {
    Write-Host "`n📊 Checking final status..." -ForegroundColor Yellow
    
    # Check ArgoCD application status
    try {
        $syncStatus = kubectl get application $AppName -n argocd -o jsonpath='{.status.sync.status}' 2>$null
        $healthStatus = kubectl get application $AppName -n argocd -o jsonpath='{.status.health.status}' 2>$null
        
        Write-Host "ArgoCD Application Status:" -ForegroundColor White
        Write-Host "  Sync Status: $syncStatus" -ForegroundColor $(if ($syncStatus -eq "Synced") { "Green" } else { "Red" })
        Write-Host "  Health Status: $healthStatus" -ForegroundColor $(if ($healthStatus -eq "Healthy") { "Green" } else { "Red" })
    } catch {
        Write-Host "❌ Could not check ArgoCD application status" -ForegroundColor Red
    }
    
    # Check pod status
    try {
        $pods = kubectl get pods -n $Namespace -l app=$AppName --no-headers 2>$null
        if ($pods) {
            Write-Host "`nPod Status:" -ForegroundColor White
            $pods -split "`n" | ForEach-Object {
                if ($_.Trim()) {
                    $fields = $_.Trim() -split '\s+'
                    $podName = $fields[0]
                    $ready = $fields[1]
                    $status = $fields[2]
                    
                    $color = if ($status -eq "Running" -and $ready -match "1/1") { "Green" } else { "Red" }
                    Write-Host "  $podName: $ready $status" -ForegroundColor $color
                }
            }
        } else {
            Write-Host "❌ No pods found" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Could not check pod status" -ForegroundColor Red
    }
    
    # Check service endpoints
    try {
        $endpoints = kubectl get endpoints $AppName -n $Namespace -o jsonpath='{.subsets[0].addresses[*].ip}' 2>$null
        if ($endpoints) {
            $endpointCount = ($endpoints -split ' ').Count
            Write-Host "`nService Endpoints: $endpointCount ready" -ForegroundColor Green
        } else {
            Write-Host "`nService Endpoints: 0 ready" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Could not check service endpoints" -ForegroundColor Red
    }
}

# Main execution
Write-Host "Application: $AppName" -ForegroundColor Gray
Write-Host "Namespace: $Namespace" -ForegroundColor Gray
Write-Host "Force Sync: $Force" -ForegroundColor Gray
Write-Host "Wait for Sync: $WaitForSync" -ForegroundColor Gray
Write-Host ""

# Check prerequisites
if (-not (Test-KubectlAvailable)) {
    Write-Host "❌ Cannot proceed without kubectl" -ForegroundColor Red
    exit 1
}

# Step 1: Sync ArgoCD application
$syncSuccess = Sync-ArgoCDApplication

if (-not $syncSuccess) {
    Write-Host "❌ ArgoCD sync failed, but continuing with deployment restart..." -ForegroundColor Yellow
}

# Step 2: Restart deployment (if sync didn't resolve the issue)
if (-not $WaitForSync -or -not $syncSuccess) {
    $restartSuccess = Restart-Deployment
    
    if (-not $restartSuccess) {
        Write-Host "❌ Deployment restart failed" -ForegroundColor Red
    }
}

# Step 3: Check final status
Start-Sleep -Seconds 10
Check-FinalStatus

Write-Host "`n✅ Fix script completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Check ArgoCD UI for application status" -ForegroundColor White
Write-Host "2. Monitor pod logs: kubectl logs -f deployment/$AppName -n $Namespace" -ForegroundColor White
Write-Host "3. Test application: kubectl port-forward svc/$AppName 8080:8080 -n $Namespace" -ForegroundColor White
Write-Host ""
Write-Host "If issues persist, run the diagnostic script:" -ForegroundColor Yellow
Write-Host "./ai-react-frontend/scripts/diagnose-health.ps1" -ForegroundColor Gray
