{{#eq APP_TYPE 'react-frontend'}}
# Headless service for React frontend (for service discovery)
apiVersion: v1
kind: Service
metadata:
  name: {{PROJECT_ID}}-headless
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: headless-service
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: {{CONTAINER_PORT}}
    targetPort: {{CONTAINER_PORT}}
    protocol: TCP
    name: http
  selector:
    app: {{PROJECT_ID}}
    app.kubernetes.io/name: {{PROJECT_ID}}
{{/eq}}
