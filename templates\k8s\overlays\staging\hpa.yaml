apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{PROJECT_ID}}-hpa
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: autoscaler
    environment: staging
    app-type: {{APP_TYPE}}
  annotations:
    autoscaling.kubernetes.io/environment: "staging"
    autoscaling.kubernetes.io/strategy: "controlled"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{PROJECT_ID}}
  minReplicas: 2
  maxReplicas: 8  # Moderate scaling for staging validation
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 65  # Scale up at 65% CPU for staging
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 75  # Scale up at 75% memory for staging
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 120  # Moderate scale up for staging
      policies:
      - type: Percent
        value: 50   # Increase by 50% in staging
        periodSeconds: 120
      - type: Pods
        value: 2    # Add max 2 pods at once
        periodSeconds: 120
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 600  # 10 minutes before scaling down
      policies:
      - type: Percent
        value: 25   # Scale down by 25% in staging
        periodSeconds: 120
      - type: Pods
        value: 1    # Remove max 1 pod at once
        periodSeconds: 120
      selectPolicy: Min
