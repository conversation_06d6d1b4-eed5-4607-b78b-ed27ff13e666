#!/usr/bin/env python3
"""
Template validation script for GitOps automation
Validates all template files for syntax errors and missing variables
"""

import os
import sys
import re
import glob
from pathlib import Path


def print_status(message, status_type="INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m",
        "ERROR": "\033[31m",
        "WARNING": "\033[33m",
        "INFO": "\033[36m"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    try:
        print(f"{color}[{status_type}] {message}{reset}")
    except UnicodeEncodeError:
        # Fallback for systems with encoding issues
        print(f"[{status_type}] {message}")


def extract_template_variables(content):
    """Extract all template variables from content"""
    # Find all {{VARIABLE}} patterns
    simple_vars = re.findall(r'\{\{([A-Z_][A-Z0-9_]*)\}\}', content)
    
    # Find variables in conditional blocks
    if_vars = re.findall(r'\{\{#if\s+([A-Z_][A-Z0-9_]*)\}\}', content)
    eq_vars = re.findall(r'\{\{#eq\s+([A-Z_][A-Z0-9_]*)\s+', content)
    
    # Combine all variables
    all_vars = set(simple_vars + if_vars + eq_vars)
    return sorted(list(all_vars))


def validate_template_syntax(content, template_file):
    """Validate template syntax for common issues"""
    errors = []
    warnings = []
    
    # Check for unmatched conditional blocks
    if_count = len(re.findall(r'\{\{#if\s+\w+\}\}', content))
    endif_count = len(re.findall(r'\{\{/if\}\}', content))
    if if_count != endif_count:
        errors.append(f"Unmatched {{#if}} blocks: {if_count} opening, {endif_count} closing")
    
    eq_count = len(re.findall(r'\{\{#eq\s+\w+\s+[\'"][^\'"]*[\'\"]\}\}', content))
    endeq_count = len(re.findall(r'\{\{/eq\}\}', content))
    if eq_count != endeq_count:
        errors.append(f"Unmatched {{#eq}} blocks: {eq_count} opening, {endeq_count} closing")
    
    # Check for malformed conditional syntax
    malformed_if = re.findall(r'\{\{#if\s*\}\}|\{\{#if\s+\}\}', content)
    if malformed_if:
        errors.append(f"Malformed {{#if}} blocks found: {len(malformed_if)}")
    
    malformed_eq = re.findall(r'\{\{#eq\s*\}\}|\{\{#eq\s+\w+\s*\}\}', content)
    if malformed_eq:
        errors.append(f"Malformed {{#eq}} blocks found: {len(malformed_eq)}")
    
    # Check for nested conditionals that might be problematic
    nested_pattern = r'\{\{#(?:if|eq)[^}]*\}\}[^{]*\{\{#(?:if|eq)[^}]*\}\}'
    nested_matches = re.findall(nested_pattern, content, re.DOTALL)
    if nested_matches:
        warnings.append(f"Found {len(nested_matches)} nested conditional blocks")
    
    return errors, warnings


def get_expected_variables():
    """Get the list of expected template variables"""
    return {
        # Basic project variables
        "PROJECT_ID", "NAMESPACE", "ENVIRONMENT", "APP_TYPE", "APP_NAME",
        "CONTAINER_IMAGE", "CONTAINER_PORT", "REPLICAS",
        
        # Resource configuration
        "MEMORY_REQUEST", "MEMORY_LIMIT", "CPU_REQUEST", "CPU_LIMIT",
        
        # Database configuration
        "DB_USER", "DB_NAME", "DB_HOST", "DB_PASSWORD",
        "ENABLE_DATABASE",
        
        # Storage configuration
        "STORAGE_SIZE", "PVC_SIZE", "ENABLE_PVC",
        
        # Service configuration
        "SERVICE_TYPE", "HEALTH_CHECK_PATH",
        
        # Secret values (base64 encoded)
        "JWT_SECRET", "JWT_SECRET_B64", "DB_PASSWORD_B64", "DB_USER_B64",
        "SMTP_USER", "SMTP_PASS", "SMTP_USER_B64", "SMTP_PASS_B64",
        "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET",
        "GOOGLE_CLIENT_ID_B64", "GOOGLE_CLIENT_SECRET_B64",
        
        # Application configuration
        "APP_URL", "API_URL", "JWT_EXPIRATION",
        
        # CORS configuration
        "CORS_ORIGINS",
        
        # SMTP configuration
        "SMTP_HOST", "SMTP_PORT", "SMTP_FROM",
        
        # OAuth2 configuration
        "GOOGLE_REDIRECT_URI", "OAUTH_SCOPES", "OAUTH_REDIRECT_URIS",
        
        # Ingress configuration
        "INGRESS_HOST", "INGRESS_PATH", "ENABLE_INGRESS"
    }


def validate_template_variables(content, template_file):
    """Validate that all template variables are expected"""
    template_vars = extract_template_variables(content)
    expected_vars = get_expected_variables()
    
    unknown_vars = []
    for var in template_vars:
        if var not in expected_vars:
            unknown_vars.append(var)
    
    return unknown_vars


def validate_template_file(template_file):
    """Comprehensive template file validation"""
    if not os.path.exists(template_file):
        return False, [f"Template file not found: {template_file}"], []
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        return False, [f"Error reading template file: {e}"], []
    
    errors = []
    warnings = []
    
    # Syntax validation
    syntax_errors, syntax_warnings = validate_template_syntax(content, template_file)
    errors.extend(syntax_errors)
    warnings.extend(syntax_warnings)
    
    # Variable validation
    unknown_vars = validate_template_variables(content, template_file)
    if unknown_vars:
        warnings.append(f"Unknown template variables: {', '.join(unknown_vars)}")
    
    # Basic YAML structure check (if it's a YAML file)
    if template_file.endswith('.yaml') or template_file.endswith('.yml'):
        if not content.strip().startswith(('apiVersion:', 'kind:', '{{#if')):
            warnings.append("Template doesn't start with expected YAML structure")
    
    return len(errors) == 0, errors, warnings


def main():
    """Main validation function"""
    print_status("GitOps Template Validation", "INFO")
    print_status("=" * 50, "INFO")
    
    # Find all template files
    templates_dir = "templates"
    if not os.path.exists(templates_dir):
        print_status(f"Templates directory not found: {templates_dir}", "ERROR")
        sys.exit(1)
    
    template_files = []
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith(('.yaml', '.yml')):
                template_files.append(os.path.join(root, file))
    
    if not template_files:
        print_status("No template files found", "WARNING")
        sys.exit(0)
    
    print_status(f"Found {len(template_files)} template files", "INFO")
    
    # Validate each template file
    total_errors = 0
    total_warnings = 0
    
    for template_file in sorted(template_files):
        rel_path = os.path.relpath(template_file)
        print_status(f"\nValidating: {rel_path}", "INFO")
        
        is_valid, errors, warnings = validate_template_file(template_file)
        
        if errors:
            total_errors += len(errors)
            print_status(f"ERROR: {len(errors)} error(s) found:", "ERROR")
            for error in errors:
                print_status(f"  - {error}", "ERROR")

        if warnings:
            total_warnings += len(warnings)
            print_status(f"WARNING: {len(warnings)} warning(s) found:", "WARNING")
            for warning in warnings:
                print_status(f"  - {warning}", "WARNING")

        if is_valid and not warnings:
            print_status("Template is valid", "SUCCESS")
    
    # Summary
    print_status("\n" + "=" * 50, "INFO")
    print_status("Validation Summary", "INFO")
    print_status(f"Templates checked: {len(template_files)}", "INFO")
    print_status(f"Total errors: {total_errors}", "ERROR" if total_errors > 0 else "INFO")
    print_status(f"Total warnings: {total_warnings}", "WARNING" if total_warnings > 0 else "INFO")
    
    if total_errors > 0:
        print_status("Template validation failed", "ERROR")
        sys.exit(1)
    else:
        print_status("All templates passed validation", "SUCCESS")
        sys.exit(0)


if __name__ == "__main__":
    main()
