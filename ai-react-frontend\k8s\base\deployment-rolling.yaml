apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-react-frontend
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: react-frontend
    version: v1.0.0
    environment: dev
  annotations:
    deployment.kubernetes.io/revision: "1"
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-react-frontend
      app.kubernetes.io/name: ai-react-frontend
  # Rolling Update Strategy - Environment Specific (will be patched by overlays)
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  # Progress deadline - Environment Specific (will be patched by overlays)
  progressDeadlineSeconds: 300
  # Revision history limit - Environment Specific (will be patched by overlays)
  revisionHistoryLimit: 5
  template:
    metadata:
      labels:
        app: ai-react-frontend
        app.kubernetes.io/name: ai-react-frontend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: react-frontend
        version: v1.0.0
        environment: dev
      annotations:
        deployment.kubernetes.io/config-hash: "arc-runners"
    spec:
      # Security Context - Environment Specific (will be patched by overlays)
      securityContext:
        runAsNonRoot: false
        runAsUser: 0
        fsGroup: 0
      # Termination Grace Period - Environment Specific (will be patched by overlays)
      terminationGracePeriodSeconds: 30
      # React Frontend - Nginx cache directory init container
      initContainers:
      - name: nginx-cache-init
        image: busybox:1.35
        command: ['sh', '-c']
        args:
        - |
          mkdir -p /var/cache/nginx/client_temp
          mkdir -p /var/cache/nginx/proxy_temp
          mkdir -p /var/cache/nginx/fastcgi_temp
          mkdir -p /var/cache/nginx/uwsgi_temp
          mkdir -p /var/cache/nginx/scgi_temp
          chown -R 101:101 /var/cache/nginx
          chmod -R 755 /var/cache/nginx
        volumeMounts:
        - mountPath: /var/cache
          name: var-cache
        securityContext:
          runAsUser: 101
          runAsNonRoot: true
      containers:
      - name: ai-react-frontend
        image: app-image  # Will be replaced by Kustomize
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: ai-react-frontend-config
        # React Frontend - Minimal environment variables
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "8080"
        # Health Checks - Application Type Specific (will be enhanced by overlays)
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 3
          timeoutSeconds: 3
          failureThreshold: 3
        # Resource Management - Environment Specific (will be patched by overlays)
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        # Security Context - Environment Specific (will be patched by overlays)
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: false
          capabilities:
            drop:
            - ALL
        # Volume Mounts for temporary files (if needed)
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: var-cache
          mountPath: /var/cache
      volumes:
      - name: tmp
        emptyDir: {}
      - name: var-cache
        emptyDir: {}
      # Node Selection and Affinity - Environment Specific (will be patched by overlays)
      nodeSelector: {}
      tolerations: []
      affinity: {}
