apiVersion: v1
kind: Secret
metadata:
  name: {{PROJECT_ID}}-secrets
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: secrets
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
  annotations:
    config.kubernetes.io/local-config: "true"
type: Opaque
data:
  {{#eq APP_TYPE 'react-frontend'}}
  # React Frontend - Minimal secrets (typically API keys for build-time)
  # Most secrets are handled by the backend API
  # Add custom secrets here if needed for your React app
  
  # Build-time API keys (if needed)
  REACT_APP_API_KEY: {{#if REACT_APP_API_KEY_B64}}{{REACT_APP_API_KEY_B64}}{{else}}UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgQVBJIGtleQ==  # PLACEHOLDER{{/if}}
  
  # Analytics keys (if needed)
  REACT_APP_ANALYTICS_KEY: {{#if REACT_APP_ANALYTICS_KEY_B64}}{{REACT_APP_ANALYTICS_KEY_B64}}{{else}}UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgYW5hbHl0aWNzIGtleQ==  # PLACEHOLDER{{/if}}
  
  {{else}}
  # Backend Application Secrets
  
  # Essential Authentication Secrets
  JWT_SECRET: {{#if JWT_SECRET_B64}}{{JWT_SECRET_B64}}{{else}}c3VwZXJzZWNyZXRrZXk=  # supersecretkey{{/if}}
  
  {{#if ENABLE_DATABASE}}
  # Database Credentials
  DB_USER: {{#if DB_USER_B64}}{{DB_USER_B64}}{{else}}cG9zdGdyZXM=  # postgres{{/if}}
  DB_PASSWORD: {{#if DB_PASSWORD_B64}}{{DB_PASSWORD_B64}}{{else}}cGFzc3dvcmQ=  # password{{/if}}
  {{/if}}
  
  # SMTP Configuration
  SMTP_USER: {{#if SMTP_USER_B64}}{{SMTP_USER_B64}}{{else}}****************************************  # <EMAIL>{{/if}}
  SMTP_PASS: {{#if SMTP_PASS_B64}}{{SMTP_PASS_B64}}{{else}}ZnFhY3RlaGFmbXpsbHR6eg==  # fqactehafmzlltzz{{/if}}
  
  # OAuth2 Configuration
  GOOGLE_CLIENT_ID: {{#if GOOGLE_CLIENT_ID_B64}}{{GOOGLE_CLIENT_ID_B64}}{{else}}MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==  # 1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com{{/if}}
  GOOGLE_CLIENT_SECRET: {{#if GOOGLE_CLIENT_SECRET_B64}}{{GOOGLE_CLIENT_SECRET_B64}}{{else}}R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=  # 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{{/if}}
  
  # Session Secrets
  SESSION_SECRET: {{#if SESSION_SECRET_B64}}{{SESSION_SECRET_B64}}{{else}}ZGVmYXVsdC1zZXNzaW9uLXNlY3JldA==  # default-session-secret{{/if}}
  
  # API Keys and External Service Secrets
  ENCRYPTION_KEY: {{#if ENCRYPTION_KEY_B64}}{{ENCRYPTION_KEY_B64}}{{else}}UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgZW5jcnlwdGlvbiBrZXk=  # PLACEHOLDER{{/if}}
  
  # Third-party API Keys
  STRIPE_SECRET_KEY: {{#if STRIPE_SECRET_KEY_B64}}{{STRIPE_SECRET_KEY_B64}}{{else}}UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgU3RyaXBlIHNlY3JldCBrZXk=  # PLACEHOLDER{{/if}}
  
  # Monitoring and Analytics
  SENTRY_DSN: {{#if SENTRY_DSN_B64}}{{SENTRY_DSN_B64}}{{else}}UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgU2VudHJ5IERTO04=  # PLACEHOLDER{{/if}}
  
  # External Service Credentials
  REDIS_PASSWORD: {{#if REDIS_PASSWORD_B64}}{{REDIS_PASSWORD_B64}}{{else}}UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgUmVkaXMgcGFzc3dvcmQ=  # PLACEHOLDER{{/if}}
  
  # File Storage Credentials
  AWS_ACCESS_KEY_ID: {{#if AWS_ACCESS_KEY_ID_B64}}{{AWS_ACCESS_KEY_ID_B64}}{{else}}UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgQVdTIGFjY2VzcyBrZXk=  # PLACEHOLDER{{/if}}
  AWS_SECRET_ACCESS_KEY: {{#if AWS_SECRET_ACCESS_KEY_B64}}{{AWS_SECRET_ACCESS_KEY_B64}}{{else}}UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgQVdTIHNlY3JldCBrZXk=  # PLACEHOLDER{{/if}}
  
  # Additional secrets from GitHub issue (if provided)
  {{#if ADDITIONAL_SECRETS}}
  {{ADDITIONAL_SECRETS}}
  {{/if}}
  
  {{/eq}}
  
  # Environment-specific secret overrides (simplified to avoid template processing issues)
  # Development environment secrets (for dev environment)
  # DEBUG_TOKEN: ZGV2LWRlYnVnLXRva2Vu  # dev-debug-token

  # Staging environment secrets (for staging environment)
  # STAGING_API_KEY: c3RhZ2luZy1hcGkta2V5  # staging-api-key

  # Production environment secrets (for production environment)
  # PROD_ENCRYPTION_KEY: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgcHJvZHVjdGlvbiBlbmNyeXB0aW9uIGtleQ==  # PLACEHOLDER
