apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{PROJECT_ID}}
  namespace: {{NAMESPACE}}
  annotations:
    deployment.kubernetes.io/revision: "1"
    deployment.kubernetes.io/strategy: "rolling-controlled"
spec:
  # Staging Rolling Update Strategy - Controlled rollouts
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%  # Conservative unavailable for staging validation
      maxSurge: 25%        # Controlled surge for staging
  
  # Moderate progress deadline for staging
  progressDeadlineSeconds: 300  # 5 minutes
  
  # Extended revision history for staging
  revisionHistoryLimit: 5
  
  template:
    metadata:
      annotations:
        deployment.kubernetes.io/environment: "staging"
        deployment.kubernetes.io/strategy: "rolling-controlled"
    spec:
      # Staging security context - enhanced security
      securityContext:
        runAsNonRoot: false
        runAsUser: 0
        fsGroup: 0
      
      # Standard termination grace period for staging
      terminationGracePeriodSeconds: 30
      
      containers:
      - name: {{PROJECT_ID}}
        # Staging resource limits - balanced for 4 vCPU/8GB cluster
        resources:
          requests:
            memory: "256Mi"   # Higher request for staging
            cpu: "200m"       # 0.2 CPU core
          limits:
            memory: "1Gi"     # 1GB limit
            cpu: "800m"       # 0.8 CPU core
        
        # Staging health checks - enhanced with startup probes
        {{#eq APP_TYPE 'react-frontend'}}
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        {{else}}
        startupProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 12
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        {{/eq}}
        
        # Staging security context - enhanced security
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
        
        # Staging environment variables
        env:
        - name: NODE_ENV
          value: "production"
        - name: DEBUG
          value: "false"
        - name: PERFORMANCE_MONITORING
          value: "true"
        
      # Staging node selection - prefer stable nodes
      nodeSelector:
        node-type: "standard"  # If available
      
      # Staging tolerations - standard workloads
      tolerations: []
      
      # Staging affinity - spread across nodes for resilience
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - {{PROJECT_ID}}
            topologyKey: kubernetes.io/hostname
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node-type
                operator: In
                values:
                - standard
