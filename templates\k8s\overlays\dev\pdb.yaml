apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{PROJECT_ID}}-pdb
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: disruption-budget
    environment: dev
    app-type: {{APP_TYPE}}
  annotations:
    policy.kubernetes.io/environment: "dev"
    policy.kubernetes.io/strategy: "permissive"
spec:
  # Development - Allow more disruption for faster updates
  minAvailable: 1  # Keep at least 1 pod running
  selector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
      environment: dev
