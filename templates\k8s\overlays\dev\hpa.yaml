apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{PROJECT_ID}}-hpa
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: autoscaler
    environment: dev
    app-type: {{APP_TYPE}}
  annotations:
    autoscaling.kubernetes.io/environment: "dev"
    autoscaling.kubernetes.io/strategy: "aggressive"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{PROJECT_ID}}
  minReplicas: 1
  maxReplicas: 5  # Conservative for development cluster
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70  # Scale up at 70% CPU for development
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80  # Scale up at 80% memory for development
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60   # Fast scale up for development
      policies:
      - type: Percent
        value: 100  # Double pods quickly in dev
        periodSeconds: 60
      - type: Pods
        value: 2    # Add max 2 pods at once
        periodSeconds: 60
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300  # 5 minutes before scaling down
      policies:
      - type: Percent
        value: 50   # Scale down by 50% in dev
        periodSeconds: 60
      - type: Pods
        value: 1    # Remove max 1 pod at once
        periodSeconds: 60
      selectPolicy: Min
