# Headless service for React frontend (for service discovery)
apiVersion: v1
kind: Service
metadata:
  name: ai-react-frontend-headless
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: headless-service
    environment: dev
    app-type: react-frontend
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: ai-react-frontend
    app.kubernetes.io/name: ai-react-frontend
