# Migration Guide: Multi-Stage Rolling Update System

This guide helps you migrate existing deployments to the new industry-standard multi-stage rolling update system.

## 🔄 Migration Overview

The new system introduces:
- **Kustomize-based structure** with base resources and environment overlays
- **Environment-specific rolling update strategies** optimized for each stage
- **Correct cluster targeting** (production → dedicated cluster)
- **Enhanced security and resource management**
- **Industry-standard health checks and monitoring**

## 📋 Pre-Migration Checklist

### 1. Backup Existing Deployments
```bash
# Backup current ArgoCD applications
kubectl get applications -n argocd -o yaml > backup-argocd-apps.yaml

# Backup current deployments
kubectl get deployments --all-namespaces -o yaml > backup-deployments.yaml
```

### 2. Verify Cluster Access
```bash
# Verify access to target clusters
kubectl config get-contexts

# Test connectivity to production cluster
kubectl --context=production-cluster get nodes

# Test connectivity to dev/staging cluster  
kubectl --context=dev-staging-cluster get nodes
```

### 3. Review Resource Capacity
- **Dev/Staging Cluster**: 4 vCPU, 8GB RAM
- **Production Cluster**: 4 vCPU, 8GB RAM
- Ensure sufficient capacity for new resource allocations

## 🚀 Migration Process

### Step 1: Update Existing Applications

For each existing application (e.g., `ai-nest-backend`):

#### 1.1 Generate New Structure
```bash
# Generate new Kustomize structure for existing app
./scripts/generate-manifests-cicd.ps1 \
  -AppName "AI Nest Backend" \
  -ProjectId "ai-nest-backend" \
  -Environment "production" \
  -DockerImage "your-registry/ai-nest-backend" \
  -DockerTag "latest" \
  -AppType "springboot-backend" \
  -EnableDatabase $true
```

#### 1.2 Compare Configurations
```bash
# Compare old vs new configurations
diff -r ai-nest-backend/k8s/ ai-nest-backend-new/k8s/overlays/production/
```

#### 1.3 Migrate Secrets and ConfigMaps
```bash
# Extract current secrets
kubectl get secret ai-nest-backend-secrets -n ai-nest-backend-production -o yaml > current-secrets.yaml

# Update new secret template with actual values
# Edit ai-nest-backend/k8s/base/secret.yaml with real base64 values
```

### Step 2: Environment-by-Environment Migration

#### 2.1 Development Environment (Low Risk)
```bash
# 1. Deploy new structure to dev
kubectl apply -f ai-nest-backend/argocd/application.yaml

# 2. Verify deployment
kubectl get pods -n ai-nest-backend-dev

# 3. Test application functionality
curl http://dev-app-url/health

# 4. Monitor rolling update
kubectl rollout status deployment/ai-nest-backend -n ai-nest-backend-dev
```

#### 2.2 Staging Environment (Medium Risk)
```bash
# 1. Generate staging configuration
./scripts/generate-manifests-cicd.ps1 \
  -Environment "staging" \
  -ProjectId "ai-nest-backend" \
  # ... other parameters

# 2. Deploy to staging
kubectl apply -f ai-nest-backend/argocd/application.yaml

# 3. Validate with comprehensive testing
# 4. Monitor performance and resource usage
```

#### 2.3 Production Environment (High Risk - Careful Planning Required)

**Pre-Production Steps:**
```bash
# 1. Schedule maintenance window
# 2. Notify stakeholders
# 3. Prepare rollback plan
# 4. Verify backup procedures
```

**Production Migration:**
```bash
# 1. Generate production configuration
./scripts/generate-manifests-cicd.ps1 \
  -Environment "production" \
  -ProjectId "ai-nest-backend" \
  # ... other parameters

# 2. Review generated manifests carefully
cat ai-nest-backend/k8s/overlays/production/deployment-patch.yaml

# 3. Verify zero-downtime strategy
# maxUnavailable: 0%, maxSurge: 33%

# 4. Deploy with monitoring
kubectl apply -f ai-nest-backend/argocd/application.yaml

# 5. Monitor rolling update progress
kubectl rollout status deployment/ai-nest-backend -n ai-nest-backend-production --timeout=600s

# 6. Verify application health
kubectl get pods -n ai-nest-backend-production
curl https://prod-app-url/health
```

### Step 3: Update CI/CD Pipelines

#### 3.1 Update Repository Dispatch Payloads
```yaml
# Old payload structure
{
  "event_type": "deploy-to-argocd",
  "client_payload": {
    "app_name": "AI Nest Backend",
    "project_id": "ai-nest-backend",
    "environment": "production"
  }
}

# New payload structure (no changes required)
# The system automatically uses overlays based on environment
```

#### 3.2 Update GitHub Actions Workflows
```yaml
# No changes required to existing workflows
# The updated generation scripts automatically create Kustomize structure
```

## 🔍 Validation Steps

### 1. Verify Rolling Update Strategy
```bash
# Check deployment strategy
kubectl describe deployment ai-nest-backend -n ai-nest-backend-production | grep -A 5 "RollingUpdateStrategy"

# Expected output for production:
# RollingUpdateStrategy:  0 max unavailable, 1 max surge
```

### 2. Verify Cluster Targeting
```bash
# Check ArgoCD application destination
kubectl get application ai-nest-backend -n argocd -o yaml | grep server

# Expected for production: e9d23ae8-213c-4746-b379-330f85c0a0cf
# Expected for dev/staging: 6be4e15d-52f9-431d-84ec-ec8cad0dff2d
```

### 3. Verify Resource Management
```bash
# Check resource quotas
kubectl get resourcequota -n ai-nest-backend-production

# Check HPA configuration
kubectl get hpa -n ai-nest-backend-production

# Check PDB configuration
kubectl get pdb -n ai-nest-backend-production
```

### 4. Verify Security Policies
```bash
# Check network policies (staging/production)
kubectl get networkpolicy -n ai-nest-backend-production

# Check pod security (production)
kubectl describe pod -n ai-nest-backend-production | grep -A 10 "Security Context"
```

## 🚨 Rollback Procedures

### Immediate Rollback (If Issues Occur)
```bash
# 1. Rollback via ArgoCD
argocd app rollback ai-nest-backend --revision=previous

# 2. Or via kubectl
kubectl rollout undo deployment/ai-nest-backend -n ai-nest-backend-production

# 3. Restore from backup if needed
kubectl apply -f backup-argocd-apps.yaml
```

### Complete Rollback to Old System
```bash
# 1. Delete new ArgoCD application
kubectl delete application ai-nest-backend -n argocd

# 2. Restore old application
kubectl apply -f backup-argocd-apps.yaml

# 3. Verify restoration
kubectl get pods -n ai-nest-backend-production
```

## 📊 Post-Migration Monitoring

### 1. Performance Metrics
- Monitor deployment times (should be faster with optimized strategies)
- Check resource utilization (should be more efficient)
- Verify zero-downtime deployments in production

### 2. Health Monitoring
- Validate enhanced health checks are working
- Monitor startup times with new probe configurations
- Check application stability

### 3. Security Validation
- Verify network policies are enforcing restrictions
- Check pod security contexts are applied
- Validate RBAC permissions

## 🔧 Troubleshooting Common Issues

### Issue: Template Variables Not Processed
```bash
# Check for unprocessed variables in generated files
grep -r "{{" ai-nest-backend/k8s/

# Solution: Verify template variable names match script parameters
```

### Issue: Deployment Stuck in Rolling Update
```bash
# Check deployment status
kubectl describe deployment ai-nest-backend -n ai-nest-backend-production

# Check pod events
kubectl get events -n ai-nest-backend-production --sort-by='.lastTimestamp'

# Solution: Verify resource limits and health check configurations
```

### Issue: ArgoCD Sync Failures
```bash
# Check ArgoCD application status
argocd app get ai-nest-backend

# Check for path issues
# Ensure ArgoCD application points to overlays/environment path
```

## 📚 Additional Resources

- [Multi-Stage Rolling Update System Documentation](multi-stage-rolling-update-system.md)
- [Cross-Cluster Setup Guide](cross-cluster-setup.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)
- [Security Best Practices](security-best-practices.md)

## ✅ Migration Checklist

- [ ] Backup existing deployments
- [ ] Verify cluster access and capacity
- [ ] Generate new Kustomize structure
- [ ] Migrate secrets and configuration
- [ ] Test in development environment
- [ ] Validate in staging environment
- [ ] Plan production migration window
- [ ] Execute production migration
- [ ] Verify rolling update strategies
- [ ] Validate cluster targeting
- [ ] Check resource management
- [ ] Verify security policies
- [ ] Monitor post-migration performance
- [ ] Update documentation and runbooks

## 🎯 Success Criteria

### Development
- ✅ Fast iteration (deployment <2min)
- ✅ Basic validation and automated recovery
- ✅ Efficient resource usage

### Staging
- ✅ Controlled rollouts (deployment <5min)
- ✅ Comprehensive testing capabilities
- ✅ Production-like resource allocation

### Production
- ✅ Zero-downtime updates (deployment <10min)
- ✅ Maximum safety and reliability
- ✅ Optimal resource utilization
- ✅ Enhanced security posture
