apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{PROJECT_ID}}
  namespace: {{NAMESPACE}}
  annotations:
    deployment.kubernetes.io/revision: "1"
    deployment.kubernetes.io/strategy: "rolling-fast"
spec:
  # Development Rolling Update Strategy - Fast iteration
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 50%  # Allow 50% unavailable for fast updates
      maxSurge: 50%        # Allow 50% surge for quick rollouts
  
  # Fast progress deadline for development
  progressDeadlineSeconds: 120  # 2 minutes
  
  # Limited revision history for development
  revisionHistoryLimit: 3
  
  template:
    metadata:
      annotations:
        deployment.kubernetes.io/environment: "dev"
        deployment.kubernetes.io/strategy: "rolling-fast"
    spec:
      # Development security context - more permissive for debugging
      securityContext:
        runAsNonRoot: false
        runAsUser: 0
        fsGroup: 0
      
      # Shorter termination grace period for faster restarts
      terminationGracePeriodSeconds: 15
      
      containers:
      - name: {{PROJECT_ID}}
        # Development resource limits - optimized for 4 vCPU/8GB cluster
        resources:
          requests:
            memory: "128Mi"   # Lower request for development
            cpu: "100m"       # 0.1 CPU core
          limits:
            memory: "512Mi"   # 512MB limit
            cpu: "500m"       # 0.5 CPU core
        
        # Development health checks - faster and more lenient
        {{#eq APP_TYPE 'react-frontend'}}
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5  # More lenient for development
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 5
          periodSeconds: 3
          timeoutSeconds: 3
          failureThreshold: 5  # More lenient for development
        {{else}}
        startupProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 12
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 15  # Faster startup for development
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 5  # More lenient for development
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 5   # Quick readiness check
          periodSeconds: 3
          timeoutSeconds: 5
          failureThreshold: 5  # More lenient for development
        {{/eq}}
        
        # Development security context - very permissive for debugging
        securityContext:
          allowPrivilegeEscalation: true   # Allow for debugging
          readOnlyRootFilesystem: false    # Allow file writes for development
          runAsNonRoot: false              # Allow root for debugging
          runAsUser: 0                     # Run as root
          privileged: true                 # Full privileges for development
          capabilities:
            add:
            - ALL
        
        # Development environment variables
        env:
        - name: NODE_ENV
          value: "development"
        - name: DEBUG
          value: "true"
        - name: VERBOSE_LOGGING
          value: "true"
        
      # Development node selection - no specific requirements
      nodeSelector: {}
      
      # Development tolerations - none needed
      tolerations: []
      
      # Development affinity - prefer spreading for testing
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - {{PROJECT_ID}}
              topologyKey: kubernetes.io/hostname
