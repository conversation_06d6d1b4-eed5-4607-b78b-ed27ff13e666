# GitOps Payload Optimization Guide

This guide addresses GitHub repository dispatch payload limitations and provides optimized payload structures for reliable deployments.

## The Problem

GitHub repository dispatch events have limitations on payload size and complexity. When using too many fields (>10 variables), you may encounter errors such as:
- "More than 10 variables in payload"
- Payload size exceeded
- Repository dispatch failures

## The Solution

We've implemented a **two-tier payload system** to handle this limitation:

### 1. Minimal Payload (Recommended)

Use this for 95% of your deployments. Contains only essential fields:

```json
{
  "app_name": "My React App",
  "project_id": "my-react-app",
  "application_type": "react-frontend",
  "environment": "production",
  "docker_image": "myorg/react-app",
  "docker_tag": "v1.2.3"
}
```

**Note:** `health_check_path` is **automatically determined** based on `application_type` and **cannot be overridden**:
- **React/Vue/Angular/Static Frontend**: `/`
- **Spring Boot Backend**: `/actuator/health`
- **Node/Python Backend**: `/health`
- **Other types**: `/health`

## Automatic Health Check Path Assignment

| Application Type | Health Check Path | Rationale |
|------------------|-------------------|-----------|
| `react-frontend` | `/` | Simple static serving |
| `vue-frontend` | `/` | Simple static serving |
| `angular-frontend` | `/` | Simple static serving |
| `static-frontend` | `/` | Simple static serving |
| `springboot-backend` | `/actuator/health` | Spring Boot Actuator standard |
| `node-backend` | `/health` | Common Node.js convention |
| `python-backend` | `/health` | Common Python/FastAPI convention |
| `web-app` | `/health` | Generic web application |
| `api` | `/health` | REST API standard |
| `microservice` | `/health` | Microservice standard |
| `worker` | `/health` | Background worker health |
| `database` | `/health` | Database health check |
| `full-stack` | `/health` | Full-stack application |

**Benefits:**
- ✅ **Reduced payload size** - One less field to include
- ✅ **Correct defaults** - Always gets the right health check path
- ✅ **No configuration errors** - Cannot accidentally set wrong path
- ✅ **Simplified CI/CD** - Less configuration required

### 2. Full Payload (Advanced)

Use only when you need to override defaults:

```json
{
  "app_name": "My React App",
  "project_id": "my-react-app",
  "application_type": "react-frontend",
  "environment": "production",
  "docker_image": "myorg/react-app",
  "docker_tag": "v1.2.3",
  "container_port": 3000,
  "source_repo": "myorg/react-app",
  "source_branch": "main",
  "commit_sha": "abc123"
}
```

**Use Cases:**
- Custom container ports
- Detailed tracking requirements

**Note:** `health_check_path` is automatically set based on `application_type` and cannot be overridden:
- React Frontend: `/`
- Spring Boot Backend: `/actuator/health`
- Other types: `/health`

## Automatic Defaults Applied

When using minimal payload, the system automatically applies these defaults:

### React Frontend (`react-frontend`)
```yaml
container_port: 80
health_check_path: "/"
database_enabled: false
resources:
  memory: 128Mi-256Mi (dev), 256Mi-512Mi (staging), 512Mi-1Gi (prod)
  cpu: 50m-200m (dev), 100m-500m (staging), 200m-1000m (prod)
```

### Spring Boot Backend (`springboot-backend`)
```yaml
container_port: 8080
health_check_path: "/actuator/health"
database_enabled: true
resources:
  memory: 512Mi-1Gi (dev), 1Gi-2Gi (staging), 2Gi-4Gi (prod)
  cpu: 250m-500m (dev), 500m-1000m (staging), 1000m-2000m (prod)
```

## Implementation Examples

### React Frontend CI/CD

```yaml
name: Deploy React App
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Build and Push
        run: |
          docker build -t myorg/react-app:${{ github.sha }} .
          docker push myorg/react-app:${{ github.sha }}
      
      - name: Deploy via GitOps (Minimal Payload)
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          repository: ChidhagniConsulting/gitops-argocd-apps
          event-type: deploy-to-argocd
          client-payload: |
            {
              "app_name": "React App",
              "project_id": "react-app",
              "application_type": "react-frontend",
              "environment": "production",
              "docker_image": "myorg/react-app",
              "docker_tag": "${{ github.sha }}"
            }
```

### Spring Boot Backend CI/CD

```yaml
name: Deploy Spring Boot API
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Build and Push
        run: |
          mvn clean package
          docker build -t myorg/api:${{ github.sha }} .
          docker push myorg/api:${{ github.sha }}
      
      - name: Deploy via GitOps (Minimal Payload)
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          repository: ChidhagniConsulting/gitops-argocd-apps
          event-type: deploy-to-argocd
          client-payload: |
            {
              "app_name": "Auth API",
              "project_id": "auth-api",
              "application_type": "springboot-backend",
              "environment": "production",
              "docker_image": "myorg/api",
              "docker_tag": "${{ github.sha }}"
            }
```

## Migration Guide

### From Old Payload Structure

**Before (11 fields - may fail):**
```json
{
  "app_name": "My App",
  "project_id": "my-app",
  "application_type": "react-frontend",
  "environment": "production", 
  "docker_image": "myorg/my-app",
  "docker_tag": "v1.0.0",
  "container_port": 80,
  "health_check_path": "/",
  "source_repo": "myorg/my-app",
  "source_branch": "main",
  "commit_sha": "abc123"
}
```

**After (6 fields - reliable):**
```json
{
  "app_name": "My App",
  "project_id": "my-app",
  "application_type": "react-frontend",
  "environment": "production",
  "docker_image": "myorg/my-app", 
  "docker_tag": "v1.0.0"
}
```

### Validation

Test your payload with curl:

```bash
# Test minimal payload
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "Test App",
      "project_id": "test-app",
      "application_type": "react-frontend",
      "environment": "dev",
      "docker_image": "nginx",
      "docker_tag": "alpine"
    }
  }'
```

## Troubleshooting

### Common Issues

1. **"More than 10 variables in payload"**
   - **Solution**: Use minimal payload (6 fields)
   - **Check**: Count your JSON fields

2. **"Payload size exceeded"**
   - **Solution**: Remove optional tracking fields
   - **Check**: Use shorter values where possible

3. **"Repository dispatch failed"**
   - **Solution**: Validate JSON syntax
   - **Check**: Ensure all required fields are present

### Debugging

```bash
# Check payload field count
echo '{"app_name":"test","project_id":"test","application_type":"react-frontend","environment":"dev","docker_image":"nginx","docker_tag":"alpine"}' | jq 'keys | length'
# Should return: 6

# Validate JSON syntax
echo '{"app_name":"test",...}' | jq .
```

## Best Practices

1. **Always use minimal payload** unless you need custom overrides
2. **Test with curl** before implementing in CI/CD
3. **Use application_type** to get optimized defaults
4. **Keep field values concise** to minimize payload size
5. **Validate JSON syntax** in your CI/CD pipeline

## Backward Compatibility

- ✅ Existing applications continue to work
- ✅ Old payload structures are supported
- ✅ Gradual migration is possible
- ✅ No breaking changes to templates

The system gracefully handles both minimal and full payloads, applying intelligent defaults based on application type.


