apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-react-frontend
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-react-frontend
    app.kubernetes.io/component: application
    app.kubernetes.io/part-of: ai-react-frontend
    environment: dev
    app-type: react-frontend
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  project: ai-react-frontend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: ai-react-frontend/k8s/overlays/dev
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-react-frontend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
      - RespectIgnoreDifferences=true
      - ApplyOutOfSyncOnly=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 3
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  - group: autoscaling
    kind: HorizontalPodAutoscaler
    jsonPointers:
    - /spec/targetRef/apiVersion
  - group: ""
    kind: ConfigMap
    jsonPointers:
    - /data/deployment-timestamp
  info:
  - name: Description
    value: "AI React Frontend - React Frontend Application"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "dev"
  - name: Target Cluster
    value: "6be4e15d-52f9-431d-84ec-ec8cad0dff2d (Dev/Staging)"
  - name: Application Type
    value: "react-frontend"
  - name: Deployment Strategy
    value: "Rolling Fast (50% unavailable, 50% surge)"
  - name: Configuration
    value: "Static serving, build-time env vars"
  - name: Resource Limits
    value: "CPU: 100m-500m, Memory: 128Mi-512Mi"
