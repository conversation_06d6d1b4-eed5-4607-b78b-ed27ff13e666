apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{PROJECT_ID}}-psp
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: security-policy
    environment: production
    app-type: {{APP_TYPE}}
  annotations:
    security.kubernetes.io/environment: "production"
    security.kubernetes.io/policy-type: "restricted"
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAsNonRoot'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  readOnlyRootFilesystem: false
  seLinux:
    rule: 'RunAsAny'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{PROJECT_ID}}-psp-user
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: security-policy-role
    environment: production
    app-type: {{APP_TYPE}}
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - {{PROJECT_ID}}-psp
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{PROJECT_ID}}-psp-binding
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: security-policy-binding
    environment: production
    app-type: {{APP_TYPE}}
roleRef:
  kind: Role
  name: {{PROJECT_ID}}-psp-user
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: default
  namespace: {{NAMESPACE}}
