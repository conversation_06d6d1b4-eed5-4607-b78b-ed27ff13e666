apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{PROJECT_ID}}-pdb
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: disruption-budget
    environment: production
    app-type: {{APP_TYPE}}
  annotations:
    policy.kubernetes.io/environment: "production"
    policy.kubernetes.io/strategy: "conservative"
spec:
  # Production - Conservative approach for high availability
  minAvailable: 75%  # Keep at least 75% of pods running
  selector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
      environment: production
