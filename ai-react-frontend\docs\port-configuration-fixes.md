# Port Configuration Fixes for ai-react-frontend

## Overview

This document summarizes the port configuration fixes applied to resolve the container port mismatch between the payload specification (port 80) and the actual ai-react-frontend configuration (port 8080).

## Issue Description

**Problem**: The GitOps automation payload specifies `container_port: 80` for React frontend applications, but the ai-react-frontend configuration was using port 8080 throughout all manifests.

**Impact**: 
- Health check failures (probes checking wrong port)
- Service connectivity issues
- ArgoCD degraded health status
- Application not accessible

## Root Cause Analysis

According to the GitOps automation documentation:

### React Frontend Default Configuration
```yaml
container_port: 80          # ✅ Correct for React frontends
health_check_path: "/"      # ✅ Correct
```

### Spring Boot Backend Default Configuration  
```yaml
container_port: 8080        # ✅ Correct for Spring Boot
health_check_path: "/actuator/health"  # ✅ Correct
```

The ai-react-frontend was incorrectly configured with backend port settings.

## Files Fixed

### 1. ConfigMap Configuration
**File**: `ai-react-frontend/k8s/base/configmap.yaml`

**Changes**:
```yaml
# Before
PORT: "8080"
REACT_APP_API_URL: "http://ai-react-frontend:8080"

# After  
PORT: "80"
REACT_APP_API_URL: "http://ai-react-frontend:80"
```

### 2. Base Deployment Configuration
**File**: `ai-react-frontend/k8s/base/deployment-rolling.yaml`

**Changes**:
```yaml
# Container Port
ports:
- containerPort: 80  # Changed from 8080
  name: http
  protocol: TCP

# Environment Variable
env:
- name: PORT
  value: "80"  # Changed from "8080"

# Health Probes
startupProbe:
  httpGet:
    path: /
    port: 80  # Changed from 8080

livenessProbe:
  httpGet:
    path: /
    port: 80  # Changed from 8080

readinessProbe:
  httpGet:
    path: /
    port: 80  # Changed from 8080
```

### 3. Service Configuration
**File**: `ai-react-frontend/k8s/base/service.yaml`

**Changes**:
```yaml
# Before
ports:
- port: 8080
  targetPort: 8080
  protocol: TCP
  name: http

# After
ports:
- port: 80
  targetPort: 80
  protocol: TCP
  name: http
```

### 4. Headless Service Configuration
**File**: `ai-react-frontend/k8s/base/service-headless.yaml`

**Changes**:
```yaml
# Before
ports:
- port: 8080
  targetPort: 8080
  protocol: TCP
  name: http

# After
ports:
- port: 80
  targetPort: 80
  protocol: TCP
  name: http
```

### 5. Development Deployment Patch
**File**: `ai-react-frontend/k8s/overlays/dev/deployment-patch.yaml`

**Changes**:
```yaml
# Health Probes
startupProbe:
  httpGet:
    path: /
    port: 80  # Changed from 8080

livenessProbe:
  httpGet:
    path: /
    port: 80  # Changed from 8080

readinessProbe:
  httpGet:
    path: /
    port: 80  # Changed from 8080
```

### 6. Scripts and Documentation
**Files Updated**:
- `ai-react-frontend/scripts/fix-degraded-health.ps1`
- `ai-react-frontend/docs/troubleshooting-degraded-health.md`

**Changes**:
```bash
# Port forwarding commands updated
kubectl port-forward svc/ai-react-frontend 8080:80 -n ai-react-frontend-dev
```

## Validation

### Automated Validation Script
A new validation script has been created: `ai-react-frontend/scripts/validate-port-configuration.ps1`

**Usage**:
```bash
# Basic validation
./ai-react-frontend/scripts/validate-port-configuration.ps1

# Verbose validation with connectivity test
./ai-react-frontend/scripts/validate-port-configuration.ps1 -Verbose

# Check specific port
./ai-react-frontend/scripts/validate-port-configuration.ps1 -ExpectedPort 80
```

### Manual Validation Commands

#### 1. Check Configuration Files
```bash
# Check ConfigMap
grep -n "PORT\|port" ai-react-frontend/k8s/base/configmap.yaml

# Check Deployment
grep -n "containerPort\|port:" ai-react-frontend/k8s/base/deployment-rolling.yaml

# Check Services
grep -n "port\|targetPort" ai-react-frontend/k8s/base/service*.yaml
```

#### 2. Check Deployed Resources
```bash
# Check deployment container port
kubectl get deployment ai-react-frontend -n ai-react-frontend-dev -o jsonpath='{.spec.template.spec.containers[0].ports[0].containerPort}'

# Check service ports
kubectl get svc ai-react-frontend -n ai-react-frontend-dev -o jsonpath='{.spec.ports[0].port}:{.spec.ports[0].targetPort}'

# Check health probe ports
kubectl get deployment ai-react-frontend -n ai-react-frontend-dev -o yaml | grep -A 5 -B 5 "port:"
```

#### 3. Test Connectivity
```bash
# Port forward to test
kubectl port-forward svc/ai-react-frontend 8080:80 -n ai-react-frontend-dev

# Test in another terminal
curl http://localhost:8080/
```

## Application Steps

### 1. Apply Configuration Changes
```bash
# Apply the updated manifests
kubectl apply -k ai-react-frontend/k8s/overlays/dev
```

### 2. Restart Deployment
```bash
# Restart to pick up new configuration
kubectl rollout restart deployment/ai-react-frontend -n ai-react-frontend-dev

# Wait for rollout to complete
kubectl rollout status deployment/ai-react-frontend -n ai-react-frontend-dev
```

### 3. Verify Health Status
```bash
# Check pod status
kubectl get pods -n ai-react-frontend-dev -l app=ai-react-frontend

# Check ArgoCD application status
kubectl get application ai-react-frontend -n argocd -o jsonpath='{.status.health.status}'
```

## Prevention Measures

### 1. Template Consistency
Ensure that new React frontend applications use the correct port configuration from templates:

```yaml
# templates/k8s/base/configmap.yaml
PORT: "{{CONTAINER_PORT}}"  # Will be 80 for react-frontend

# templates/k8s/base/deployment.yaml  
containerPort: {{CONTAINER_PORT}}  # Will be 80 for react-frontend
```

### 2. Validation in CI/CD
Add port validation to the GitOps automation pipeline:

```bash
# In generate-manifests-cicd.yaml
case "$APPLICATION_TYPE" in
  "react-frontend"|"vue-frontend"|"angular-frontend"|"static-frontend")
    CONTAINER_PORT="${CONTAINER_PORT:-80}"  # ✅ Correct default
    ;;
  "springboot-backend")
    CONTAINER_PORT="${CONTAINER_PORT:-8080}"  # ✅ Correct default
    ;;
esac
```

### 3. Documentation Updates
- Update application type documentation to clearly specify port requirements
- Add port configuration validation to troubleshooting guides
- Include port checks in health diagnostic scripts

## Related Issues

This fix resolves several related issues:

1. **Health Check Failures**: Probes now check the correct port (80)
2. **Service Connectivity**: Services now route to the correct container port
3. **ArgoCD Degraded Status**: Application should now show healthy status
4. **Container Startup Issues**: Proper port configuration prevents startup failures

## Testing Checklist

- [ ] All configuration files updated to use port 80
- [ ] Deployment successfully rolls out
- [ ] Pods are running and ready (1/1)
- [ ] Health probes are passing
- [ ] Service endpoints are available
- [ ] ArgoCD application shows "Healthy" status
- [ ] Application is accessible via port forwarding
- [ ] No port 8080 references remain in configuration

## Support

If issues persist after applying these fixes:

1. Run the validation script: `./ai-react-frontend/scripts/validate-port-configuration.ps1 -Verbose`
2. Check the troubleshooting guide: `ai-react-frontend/docs/troubleshooting-degraded-health.md`
3. Review ArgoCD application logs and events
4. Verify the container image is built to serve on port 80
