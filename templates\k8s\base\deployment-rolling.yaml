apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{PROJECT_ID}}
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: {{APP_TYPE}}
    version: v1.0.0
    environment: {{ENVIRONMENT}}
  annotations:
    deployment.kubernetes.io/revision: "1"
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: {{REPLICAS}}
  selector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
  # Rolling Update Strategy - Environment Specific (will be patched by overlays)
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  # Progress deadline - Environment Specific (will be patched by overlays)
  progressDeadlineSeconds: 300
  # Revision history limit - Environment Specific (will be patched by overlays)
  revisionHistoryLimit: 5
  template:
    metadata:
      labels:
        app: {{PROJECT_ID}}
        app.kubernetes.io/name: {{PROJECT_ID}}
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: {{APP_TYPE}}
        version: v1.0.0
        environment: {{ENVIRONMENT}}
      annotations:
        deployment.kubernetes.io/config-hash: "{{DOCKER_TAG}}"
    spec:
      # Security Context - Environment Specific (will be patched by overlays)
      securityContext:
        runAsNonRoot: false
        runAsUser: 0
        fsGroup: 0
      # Termination Grace Period - Environment Specific (will be patched by overlays)
      terminationGracePeriodSeconds: 30
      {{#eq APP_TYPE 'react-frontend'}}
      # React Frontend - Nginx cache directory init container
      initContainers:
      - name: nginx-cache-init
        image: busybox:1.35
        command: ['sh', '-c']
        args:
        - |
          mkdir -p /var/cache/nginx/client_temp
          mkdir -p /var/cache/nginx/proxy_temp
          mkdir -p /var/cache/nginx/fastcgi_temp
          mkdir -p /var/cache/nginx/uwsgi_temp
          mkdir -p /var/cache/nginx/scgi_temp
          chown -R 101:101 /var/cache/nginx
          chmod -R 755 /var/cache/nginx
        volumeMounts:
        - mountPath: /var/cache
          name: var-cache
        securityContext:
          runAsUser: 101
          runAsNonRoot: true
      {{else}}
      {{#if ENABLE_DATABASE}}
      # Backend Applications - Database init container
      initContainers:
      - name: wait-for-postgres
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h {{#if DB_HOST}}{{DB_HOST}}{{else}}{{PROJECT_ID}}-postgres{{/if}} -p 5432 -U {{DB_USER}}; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      {{/if}}
      {{/eq}}
      containers:
      - name: {{PROJECT_ID}}
        image: app-image  # Will be replaced by Kustomize
        imagePullPolicy: Always
        ports:
        - containerPort: {{CONTAINER_PORT}}
          name: http
          protocol: TCP
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: {{PROJECT_ID}}-config
        {{#eq APP_TYPE 'react-frontend'}}
        # React Frontend - Minimal environment variables
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "{{CONTAINER_PORT}}"
        {{else}}
        # Backend Applications - Full environment variables
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "{{CONTAINER_PORT}}"
        {{#if ENABLE_DATABASE}}
        # Database Configuration
        - name: DB_HOST
          value: "{{#if DB_HOST}}{{DB_HOST}}{{else}}{{PROJECT_ID}}-postgres{{/if}}"
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: "{{DB_NAME}}"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
        {{/if}}
        # Authentication Secrets
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: JWT_SECRET
        # OAuth Configuration
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_SECRET
        {{/eq}}
        # Health Checks - Application Type Specific (will be enhanced by overlays)
        {{#eq APP_TYPE 'react-frontend'}}
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 5
          periodSeconds: 3
          timeoutSeconds: 3
          failureThreshold: 3
        {{else}}
        {{#eq APP_TYPE 'springboot-backend'}}
        startupProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 12
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        {{else}}
        # Default health checks for other application types
        startupProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 12
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        {{/eq}}
        {{/eq}}
        # Resource Management - Environment Specific (will be patched by overlays)
        resources:
          requests:
            memory: "{{MEMORY_REQUEST}}"
            cpu: "{{CPU_REQUEST}}"
          limits:
            memory: "{{MEMORY_LIMIT}}"
            cpu: "{{CPU_LIMIT}}"
        # Security Context - Environment Specific (will be patched by overlays)
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: false
          capabilities:
            drop:
            - ALL
        # Volume Mounts for temporary files (if needed)
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: var-cache
          mountPath: /var/cache
      volumes:
      - name: tmp
        emptyDir: {}
      - name: var-cache
        emptyDir: {}
      # Node Selection and Affinity - Environment Specific (will be patched by overlays)
      nodeSelector: {}
      tolerations: []
      affinity: {}
