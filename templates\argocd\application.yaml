apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{PROJECT_ID}}
  namespace: argocd
  labels:
    app.kubernetes.io/name: {{PROJECT_ID}}
    app.kubernetes.io/component: application
    app.kubernetes.io/part-of: {{PROJECT_ID}}
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  project: {{PROJECT_ID}}-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: {{PROJECT_ID}}/k8s/overlays/{{ENVIRONMENT}}
  destination:
    server: {{#eq ENVIRONMENT 'production'}}https://e9d23ae8-213c-4746-b379-330f85c0a0cf.k8s.ondigitalocean.com{{else}}https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com{{/eq}}
    namespace: {{NAMESPACE}}
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
      - RespectIgnoreDifferences=true
      - ApplyOutOfSyncOnly=true
    retry:
      limit: {{#eq ENVIRONMENT 'production'}}10{{else}}5{{/eq}}
      backoff:
        duration: {{#eq ENVIRONMENT 'production'}}10s{{else}}5s{{/eq}}
        factor: 2
        maxDuration: {{#eq ENVIRONMENT 'production'}}10m{{else}}3m{{/eq}}
  revisionHistoryLimit: {{#eq ENVIRONMENT 'production'}}10{{else}}{{#eq ENVIRONMENT 'staging'}}5{{else}}3{{/eq}}{{/eq}}
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  - group: autoscaling
    kind: HorizontalPodAutoscaler
    jsonPointers:
    - /spec/targetRef/apiVersion
  - group: ""
    kind: ConfigMap
    jsonPointers:
    - /data/deployment-timestamp
  info:
  - name: Description
    value: "{{APP_NAME}} - {{#eq APP_TYPE 'react-frontend'}}React Frontend Application{{else}}{{#eq APP_TYPE 'springboot-backend'}}Spring Boot Backend API{{else}}{{APP_TYPE}} deployment{{/eq}}{{/eq}}"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "{{ENVIRONMENT}}"
  - name: Target Cluster
    value: "{{#eq ENVIRONMENT 'production'}}e9d23ae8-213c-4746-b379-330f85c0a0cf (Production){{else}}6be4e15d-52f9-431d-84ec-ec8cad0dff2d (Dev/Staging){{/eq}}"
  - name: Application Type
    value: "{{APP_TYPE}}"
  - name: Deployment Strategy
    value: "{{#eq ENVIRONMENT 'production'}}Rolling Safe (0% unavailable, 33% surge){{else}}{{#eq ENVIRONMENT 'staging'}}Rolling Controlled (25% unavailable, 25% surge){{else}}Rolling Fast (50% unavailable, 50% surge){{/eq}}{{/eq}}"
  - name: Configuration
    value: "{{#eq APP_TYPE 'react-frontend'}}Static serving, build-time env vars{{else}}{{#eq APP_TYPE 'springboot-backend'}}Database integration, JWT auth, health checks{{else}}Standard configuration{{/eq}}{{/eq}}"
  - name: Resource Limits
    value: "{{#eq ENVIRONMENT 'production'}}CPU: 300m-1000m, Memory: 512Mi-2Gi{{else}}{{#eq ENVIRONMENT 'staging'}}CPU: 200m-800m, Memory: 256Mi-1Gi{{else}}CPU: 100m-500m, Memory: 128Mi-512Mi{{/eq}}{{/eq}}"

