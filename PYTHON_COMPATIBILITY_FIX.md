# Python Compatibility Fix for Windows Self-Hosted Runners

## Issue Summary

The GitOps deployment workflow was failing with "Python was not found" errors when running on Windows self-hosted runners. The issue was identified in the GitHub Actions workflow `deploy-from-cicd.yaml`.

## Root Cause

The workflow was using `python3` commands, which are not available on Windows systems where Python is typically accessed via the `python` command:

```bash
# ❌ Failing commands
python3 -m pip install --user PyYAML
python3 scripts/generate-manifests-cicd.py
python3 scripts/validate-yaml.py
```

## Error Details

- **Error Message**: "Python was not found; run without arguments to install from the Microsoft Store"
- **Affected Steps**: 
  - Python dependency installation
  - Manifest generation
  - YAML validation
  - ArgoCD path validation

## Solution

Updated all `python3` references to `python` for Windows compatibility:

### Changes Made

1. **Python Dependencies Installation**:
   ```yaml
   # Before
   python3 -m pip install --user --upgrade pip
   python3 -m pip install --user PyYAML
   
   # After
   python -m pip install --user --upgrade pip
   python -m pip install --user PyYAML
   ```

2. **Manifest Generation Script**:
   ```bash
   # Before
   python3 scripts/generate-manifests-cicd.py
   
   # After
   python scripts/generate-manifests-cicd.py
   ```

3. **Validation Scripts**:
   ```bash
   # Before
   python3 scripts/validate-argocd-paths.py
   python3 scripts/validate-yaml.py
   
   # After
   python scripts/validate-argocd-paths.py
   python scripts/validate-yaml.py
   ```

4. **Deployment Section**:
   ```bash
   # Before
   pip3 install PyYAML --user --quiet
   python3 scripts/validate-yaml.py
   
   # After
   python -m pip install PyYAML --user --quiet
   python scripts/validate-yaml.py
   ```

## Testing

Created and ran a debug script that confirmed:
- ✅ Python dependencies installation works
- ✅ Manifest generation completes successfully
- ✅ YAML validation passes for all generated files
- ✅ All template processing functions correctly

## Impact

This fix resolves the deployment failures and ensures the GitOps automation works correctly on Windows self-hosted runners while maintaining compatibility with Linux runners.

## Files Modified

- `.github/workflows/deploy-from-cicd.yaml` - Updated all Python command references

## Verification

The fix was tested locally and confirmed to:
1. Generate valid ArgoCD and Kubernetes manifests
2. Pass YAML syntax validation
3. Complete the full deployment workflow without Python-related errors

## Next Steps

The workflow should now work correctly for:
- Repository dispatch events from application CI/CD pipelines
- Automated manifest generation and deployment
- Cross-cluster ArgoCD deployments to DOKS clusters

Monitor the next deployment to confirm the fix resolves the issue completely.
