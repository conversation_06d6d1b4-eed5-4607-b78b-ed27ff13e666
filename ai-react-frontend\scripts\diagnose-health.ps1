#!/usr/bin/env pwsh
# Diagnostic script for ai-react-frontend health issues

param(
    [string]$Namespace = "ai-react-frontend-dev",
    [string]$AppName = "ai-react-frontend",
    [switch]$Fix,
    [switch]$Verbose
)

Write-Host "🔍 Diagnosing ai-react-frontend health issues..." -ForegroundColor Cyan

# Function to check pod status
function Check-PodStatus {
    Write-Host "`n📦 Checking Pod Status..." -ForegroundColor Yellow
    
    $pods = kubectl get pods -n $Namespace -l app=$AppName -o json | ConvertFrom-Json
    
    if ($pods.items.Count -eq 0) {
        Write-Host "❌ No pods found for app: $AppName in namespace: $Namespace" -ForegroundColor Red
        return $false
    }
    
    foreach ($pod in $pods.items) {
        $podName = $pod.metadata.name
        $phase = $pod.status.phase
        $ready = $pod.status.conditions | Where-Object { $_.type -eq "Ready" } | Select-Object -ExpandProperty status
        
        Write-Host "Pod: $podName" -ForegroundColor White
        Write-Host "  Phase: $phase" -ForegroundColor $(if ($phase -eq "Running") { "Green" } else { "Red" })
        Write-Host "  Ready: $ready" -ForegroundColor $(if ($ready -eq "True") { "Green" } else { "Red" })
        
        # Check container statuses
        foreach ($container in $pod.status.containerStatuses) {
            $containerName = $container.name
            $containerReady = $container.ready
            $restartCount = $container.restartCount
            
            Write-Host "  Container: $containerName" -ForegroundColor White
            Write-Host "    Ready: $containerReady" -ForegroundColor $(if ($containerReady) { "Green" } else { "Red" })
            Write-Host "    Restart Count: $restartCount" -ForegroundColor $(if ($restartCount -eq 0) { "Green" } else { "Yellow" })
            
            if ($container.state.waiting) {
                $reason = $container.state.waiting.reason
                $message = $container.state.waiting.message
                Write-Host "    Waiting: $reason - $message" -ForegroundColor Red
            }
            
            if ($container.state.terminated) {
                $reason = $container.state.terminated.reason
                $message = $container.state.terminated.message
                Write-Host "    Terminated: $reason - $message" -ForegroundColor Red
            }
        }
    }
    
    return $true
}

# Function to check service endpoints
function Check-ServiceEndpoints {
    Write-Host "`n🌐 Checking Service Endpoints..." -ForegroundColor Yellow
    
    $services = kubectl get svc -n $Namespace -l app=$AppName -o json | ConvertFrom-Json
    
    foreach ($service in $services.items) {
        $serviceName = $service.metadata.name
        Write-Host "Service: $serviceName" -ForegroundColor White
        
        $endpoints = kubectl get endpoints -n $Namespace $serviceName -o json | ConvertFrom-Json
        
        if ($endpoints.subsets -and $endpoints.subsets.Count -gt 0) {
            $readyAddresses = $endpoints.subsets[0].addresses
            if ($readyAddresses) {
                Write-Host "  ✅ Ready Endpoints: $($readyAddresses.Count)" -ForegroundColor Green
            } else {
                Write-Host "  ❌ No ready endpoints" -ForegroundColor Red
            }
        } else {
            Write-Host "  ❌ No endpoints found" -ForegroundColor Red
        }
    }
}

# Function to check events
function Check-Events {
    Write-Host "`n📋 Checking Recent Events..." -ForegroundColor Yellow
    
    $events = kubectl get events -n $Namespace --sort-by='.lastTimestamp' --field-selector involvedObject.name=$AppName -o json | ConvertFrom-Json
    
    if ($events.items.Count -eq 0) {
        Write-Host "No recent events found for $AppName" -ForegroundColor Gray
        return
    }
    
    $recentEvents = $events.items | Select-Object -Last 10
    
    foreach ($event in $recentEvents) {
        $type = $event.type
        $reason = $event.reason
        $message = $event.message
        $time = $event.lastTimestamp
        
        $color = switch ($type) {
            "Warning" { "Red" }
            "Normal" { "Green" }
            default { "White" }
        }
        
        Write-Host "[$time] $type - $reason: $message" -ForegroundColor $color
    }
}

# Function to check image availability
function Check-ImageAvailability {
    Write-Host "`n🐳 Checking Image Availability..." -ForegroundColor Yellow
    
    $deployment = kubectl get deployment -n $Namespace $AppName -o json | ConvertFrom-Json
    $image = $deployment.spec.template.spec.containers[0].image
    
    Write-Host "Image: $image" -ForegroundColor White
    
    # Try to pull the image info
    try {
        $imageInfo = docker manifest inspect $image 2>$null
        if ($imageInfo) {
            Write-Host "  ✅ Image exists and is accessible" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Image may not exist or is not accessible" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ⚠️  Cannot verify image (docker not available)" -ForegroundColor Yellow
    }
}

# Function to check HPA status
function Check-HPAStatus {
    Write-Host "`n📈 Checking HPA Status..." -ForegroundColor Yellow
    
    try {
        $hpa = kubectl get hpa -n $Namespace "${AppName}-hpa" -o json | ConvertFrom-Json
        
        $currentReplicas = $hpa.status.currentReplicas
        $desiredReplicas = $hpa.status.desiredReplicas
        $minReplicas = $hpa.spec.minReplicas
        $maxReplicas = $hpa.spec.maxReplicas
        
        Write-Host "HPA: ${AppName}-hpa" -ForegroundColor White
        Write-Host "  Current Replicas: $currentReplicas" -ForegroundColor White
        Write-Host "  Desired Replicas: $desiredReplicas" -ForegroundColor White
        Write-Host "  Min/Max Replicas: $minReplicas/$maxReplicas" -ForegroundColor White
        
        if ($hpa.status.conditions) {
            foreach ($condition in $hpa.status.conditions) {
                $type = $condition.type
                $status = $condition.status
                $reason = $condition.reason
                $message = $condition.message
                
                $color = if ($status -eq "True") { "Green" } else { "Red" }
                Write-Host "  Condition: $type = $status ($reason)" -ForegroundColor $color
                if ($message) {
                    Write-Host "    Message: $message" -ForegroundColor Gray
                }
            }
        }
    } catch {
        Write-Host "  ⚠️  HPA not found or not accessible" -ForegroundColor Yellow
    }
}

# Function to apply fixes
function Apply-Fixes {
    Write-Host "`n🔧 Applying fixes..." -ForegroundColor Cyan
    
    Write-Host "1. Restarting deployment..." -ForegroundColor Yellow
    kubectl rollout restart deployment/$AppName -n $Namespace
    
    Write-Host "2. Waiting for rollout to complete..." -ForegroundColor Yellow
    kubectl rollout status deployment/$AppName -n $Namespace --timeout=300s
    
    Write-Host "3. Checking pod status after restart..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    Check-PodStatus
}

# Main execution
Write-Host "Namespace: $Namespace" -ForegroundColor Gray
Write-Host "App Name: $AppName" -ForegroundColor Gray
Write-Host ""

Check-PodStatus
Check-ServiceEndpoints
Check-Events
Check-ImageAvailability
Check-HPAStatus

if ($Fix) {
    Apply-Fixes
}

Write-Host "`n✅ Diagnosis complete!" -ForegroundColor Green
Write-Host "If issues persist, check ArgoCD application sync status and consider manual sync." -ForegroundColor Gray
