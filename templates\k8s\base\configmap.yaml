apiVersion: v1
kind: ConfigMap
metadata:
  name: {{PROJECT_ID}}-config
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: config
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
  annotations:
    config.kubernetes.io/local-config: "true"
data:
  # Application Configuration
  APP_NAME: "{{APP_NAME}}"
  PROJECT_ID: "{{PROJECT_ID}}"
  ENVIRONMENT: "{{ENVIRONMENT}}"
  NODE_ENV: "production"
  PORT: "{{CONTAINER_PORT}}"
  
  {{#eq APP_TYPE 'react-frontend'}}
  # React Frontend Configuration
  REACT_APP_NAME: "{{APP_NAME}}"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_ENVIRONMENT: "{{ENVIRONMENT}}"
  REACT_APP_API_URL: "{{#if API_URL}}{{API_URL}}{{else}}http://localhost:3001{{/if}}"
  REACT_APP_PUBLIC_URL: "{{#if PUBLIC_URL}}{{PUBLIC_URL}}{{else}}/{{/if}}"
  
  # Build Configuration
  GENERATE_SOURCEMAP: "false"
  INLINE_RUNTIME_CHUNK: "false"
  IMAGE_INLINE_SIZE_LIMIT: "10000"
  
  {{else}}
  # Backend Application Configuration
  API_URL: "{{#if API_URL}}{{API_URL}}{{else}}http://localhost:{{CONTAINER_PORT}}{{/if}}"
  APP_URL: "{{#if APP_URL}}{{APP_URL}}{{else}}http://localhost:3000{{/if}}"
  PUBLIC_URL: "{{#if PUBLIC_URL}}{{PUBLIC_URL}}{{else}}http://localhost:3000{{/if}}"
  
  # CORS Configuration
  CORS_ORIGINS: "{{#if CORS_ORIGINS}}{{CORS_ORIGINS}}{{else}}http://localhost:3000,http://localhost:3001{{/if}}"
  CORS_CREDENTIALS: "true"
  
  # JWT Configuration
  JWT_EXPIRATION: "{{#if JWT_EXPIRATION}}{{JWT_EXPIRATION}}{{else}}86400000{{/if}}"
  
  {{#if ENABLE_DATABASE}}
  # Database Configuration
  DB_HOST: "{{#eq ENVIRONMENT 'dev'}}localhost{{else}}{{PROJECT_ID}}-postgres.{{NAMESPACE}}.svc.cluster.local{{/eq}}"
  DB_PORT: "5432"
  DB_NAME: "{{DB_NAME}}"
  DB_SSL: "{{#eq ENVIRONMENT 'production'}}true{{else}}false{{/eq}}"
  DB_POOL_MIN: "{{#eq ENVIRONMENT 'production'}}5{{else}}2{{/eq}}"
  DB_POOL_MAX: "{{#eq ENVIRONMENT 'production'}}20{{else}}10{{/eq}}"
  DB_TIMEOUT: "{{#eq ENVIRONMENT 'production'}}30000{{else}}10000{{/eq}}"
  {{/if}}
  
  # SMTP Configuration
  SMTP_HOST: "{{#if SMTP_HOST}}{{SMTP_HOST}}{{else}}smtp.gmail.com{{/if}}"
  SMTP_PORT: "{{#if SMTP_PORT}}{{SMTP_PORT}}{{else}}587{{/if}}"
  SMTP_SECURE: "{{#if SMTP_SECURE}}{{SMTP_SECURE}}{{else}}false{{/if}}"
  SMTP_FROM: "{{#if SMTP_FROM}}{{SMTP_FROM}}{{else}}<EMAIL>{{/if}}"
  
  # OAuth Configuration
  GOOGLE_CALLBACK_URL: "{{#if GOOGLE_CALLBACK_URL}}{{GOOGLE_CALLBACK_URL}}{{else}}http://localhost:{{CONTAINER_PORT}}/auth/google/callback{{/if}}"
  
  # Session Configuration
  SESSION_SECRET: "{{#if SESSION_SECRET}}{{SESSION_SECRET}}{{else}}default-session-secret{{/if}}"
  SESSION_MAX_AGE: "{{#if SESSION_MAX_AGE}}{{SESSION_MAX_AGE}}{{else}}86400000{{/if}}"
  
  {{/eq}}
  
  # Health Check Configuration
  HEALTH_CHECK_PATH: "{{HEALTH_CHECK_PATH}}"
  HEALTH_CHECK_INTERVAL: "{{#eq ENVIRONMENT 'production'}}10000{{else}}30000{{/eq}}"
  
  # Logging Configuration
  LOG_LEVEL: "{{#eq ENVIRONMENT 'production'}}info{{else}}debug{{/eq}}"
  LOG_FORMAT: "json"
  LOG_TIMESTAMP: "true"
  
  # Performance Configuration
  MAX_REQUEST_SIZE: "{{#eq ENVIRONMENT 'production'}}10mb{{else}}50mb{{/eq}}"
  REQUEST_TIMEOUT: "{{#eq ENVIRONMENT 'production'}}30000{{else}}60000{{/eq}}"
  
  # Security Configuration
  HELMET_ENABLED: "{{#eq ENVIRONMENT 'production'}}true{{else}}false{{/eq}}"
  RATE_LIMIT_ENABLED: "{{#eq ENVIRONMENT 'production'}}true{{else}}false{{/eq}}"
  RATE_LIMIT_MAX: "{{#eq ENVIRONMENT 'production'}}100{{else}}1000{{/eq}}"
  RATE_LIMIT_WINDOW: "900000"  # 15 minutes
  
  # Application Monitoring Configuration (without PLG stack)
  HEALTH_MONITORING_ENABLED: "true"
  HEALTH_MONITORING_PATH: "{{HEALTH_CHECK_PATH}}"
  APPLICATION_METRICS_ENABLED: "{{#eq ENVIRONMENT 'production'}}true{{else}}false{{/eq}}"
  
  # Cache Configuration
  CACHE_TTL: "{{#eq ENVIRONMENT 'production'}}3600{{else}}300{{/eq}}"
  CACHE_MAX_SIZE: "{{#eq ENVIRONMENT 'production'}}1000{{else}}100{{/eq}}"
  
  # Feature Flags
  FEATURE_REGISTRATION: "{{#eq ENVIRONMENT 'production'}}false{{else}}true{{/eq}}"
  FEATURE_DEBUG_MODE: "{{#eq ENVIRONMENT 'production'}}false{{else}}true{{/eq}}"
  FEATURE_ANALYTICS: "{{#eq ENVIRONMENT 'production'}}true{{else}}false{{/eq}}"
  
  # Environment-specific overrides
  {{#eq ENVIRONMENT 'dev'}}
  # Development specific configuration
  DEBUG: "true"
  VERBOSE_LOGGING: "true"
  HOT_RELOAD: "true"
  {{/eq}}
  
  {{#eq ENVIRONMENT 'staging'}}
  # Staging specific configuration
  DEBUG: "false"
  VERBOSE_LOGGING: "true"
  PERFORMANCE_MONITORING: "true"
  {{/eq}}
  
  {{#eq ENVIRONMENT 'production'}}
  # Production specific configuration
  DEBUG: "false"
  VERBOSE_LOGGING: "false"
  PERFORMANCE_MONITORING: "true"
  ERROR_REPORTING: "true"
  {{/eq}}
