#!/usr/bin/env python3
"""
Verify that health check path automation is working correctly
"""

import json
import subprocess
import sys

def test_health_check_automation():
    """Test that health check paths are correctly auto-determined"""
    
    test_cases = [
        {
            "application_type": "react-frontend",
            "expected_health_path": "/",
            "expected_port": 80
        },
        {
            "application_type": "springboot-backend", 
            "expected_health_path": "/actuator/health",
            "expected_port": 8080
        },
        {
            "application_type": "node-backend",
            "expected_health_path": "/health", 
            "expected_port": 8080
        },
        {
            "application_type": "python-backend",
            "expected_health_path": "/health",
            "expected_port": 8080
        }
    ]
    
    print("🧪 Testing health check path automation...")
    
    for test_case in test_cases:
        app_type = test_case["application_type"]
        expected_health = test_case["expected_health_path"]
        expected_port = test_case["expected_port"]
        
        print(f"\n📋 Testing {app_type}...")
        
        # Test with Python script
        cmd = [
            "python3", "scripts/generate-manifests-cicd.py",
            "--app-name", f"Test {app_type}",
            "--project-id", f"test-{app_type}",
            "--application-type", app_type,
            "--environment", "dev",
            "--docker-image", "test/image",
            "--docker-tag", "latest",
            "--output-dir", "/tmp"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Check if health check path was correctly determined
            if f"Health check path auto-determined: {expected_health}" in result.stdout:
                print(f"  ✅ Health check path: {expected_health}")
            else:
                print(f"  ❌ Health check path not correctly determined")
                print(f"     Expected: {expected_health}")
                print(f"     Output: {result.stdout}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Script execution failed: {e}")
            print(f"     stdout: {e.stdout}")
            print(f"     stderr: {e.stderr}")
            return False
    
    print("\n🎉 All health check automation tests passed!")
    return True

if __name__ == "__main__":
    if test_health_check_automation():
        print("✅ Health check automation verification successful!")
        sys.exit(0)
    else:
        print("❌ Health check automation verification failed!")
        sys.exit(1)