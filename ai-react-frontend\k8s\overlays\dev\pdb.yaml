apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: ai-react-frontend-pdb
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: disruption-budget
    environment: dev
    app-type: react-frontend
  annotations:
    policy.kubernetes.io/environment: "dev"
    policy.kubernetes.io/strategy: "permissive"
spec:
  # Development - Allow more disruption for faster updates
  maxUnavailable: 1  # Allow 1 pod to be unavailable during updates
  selector:
    matchLabels:
      app: ai-react-frontend
      app.kubernetes.io/name: ai-react-frontend
      environment: dev
